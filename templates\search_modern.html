{% extends "base_modern.html" %}

{% block title %}Search - Graphiti{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Advanced Search</h1>
        <p class="text-muted">Search across documents, entities, and knowledge graph</p>
    </div>
    <div>
        <button class="btn btn-outline-primary" onclick="toggleAdvancedSearch()">
            <i class="bi bi-sliders"></i> Advanced
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="clearSearch()">
            <i class="bi bi-x-circle"></i> Clear
        </button>
    </div>
</div>

<!-- Search Interface -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="input-group input-group-lg">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-query"
                           placeholder="Search documents, entities, or ask a question...">
                    <button class="btn btn-primary" type="button" onclick="performSearch()">
                        <i class="bi bi-search"></i> Search
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select form-select-lg" id="search-type">
                    <option value="semantic">Semantic Search</option>
                    <option value="keyword">Keyword Search</option>
                    <option value="entity">Entity Search</option>
                    <option value="hybrid">Hybrid Search</option>
                </select>
            </div>
        </div>

        <!-- Advanced Search Options -->
        <div id="advanced-search" class="mt-4" style="display: none;">
            <div class="row">
                <div class="col-md-3">
                    <label for="document-filter" class="form-label">Document Filter</label>
                    <select class="form-select" id="document-filter">
                        <option value="">All Documents</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="entity-type-filter" class="form-label">Entity Type</label>
                    <select class="form-select" id="entity-type-filter">
                        <option value="">All Types</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date-range" class="form-label">Date Range</label>
                    <select class="form-select" id="date-range">
                        <option value="">All Time</option>
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 3 months</option>
                        <option value="365">Last year</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="similarity-threshold" class="form-label">Similarity</label>
                    <input type="range" class="form-range" id="similarity-threshold"
                           min="0.1" max="1.0" step="0.1" value="0.7">
                    <small class="text-muted">Threshold: <span id="similarity-value">0.7</span></small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
<div class="row">
    <!-- Results Panel -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-list-ul"></i> Search Results</h5>
                <div>
                    <span class="badge bg-primary" id="results-count">0 results</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="exportResults()">
                        <i class="bi bi-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- Loading State -->
                <div id="search-loading" class="text-center py-5" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Searching...</p>
                </div>

                <!-- Welcome State -->
                <div id="welcome-state" class="text-center py-5">
                    <i class="bi bi-search fs-1 text-muted mb-3"></i>
                    <h5>Start Your Search</h5>
                    <p class="text-muted">Enter a query to search across your knowledge base</p>
                    <div class="mt-4">
                        <h6>Example Searches:</h6>
                        <div class="d-flex flex-wrap justify-content-center gap-2 mt-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="setExampleQuery('machine learning algorithms')">
                                machine learning algorithms
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="setExampleQuery('diabetes treatment')">
                                diabetes treatment
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="setExampleQuery('research methodology')">
                                research methodology
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Results List -->
                <div id="results-container" style="display: none;">
                    <div id="results-list"></div>
                </div>

                <!-- Empty Results -->
                <div id="empty-results" class="text-center py-5" style="display: none;">
                    <i class="bi bi-search fs-1 text-muted mb-3"></i>
                    <h5>No Results Found</h5>
                    <p class="text-muted">Try adjusting your search terms or filters</p>
                    <button class="btn btn-primary" onclick="clearSearch()">
                        <i class="bi bi-arrow-clockwise"></i> Try Again
                    </button>
                </div>
            </div>

            <!-- Pagination -->
            <div class="card-footer" id="results-pagination" style="display: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">
                            Showing <span id="showing-start">0</span> to <span id="showing-end">0</span>
                            of <span id="total-results">0</span> results
                        </small>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="pagination">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Search Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h6><i class="bi bi-graph-up"></i> Search Analytics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary" id="total-documents">-</div>
                        <small class="text-muted">Documents</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success" id="total-entities">-</div>
                        <small class="text-muted">Entities</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <div class="h5 text-info" id="search-time">-</div>
                    <small class="text-muted">Search Time (ms)</small>
                </div>
            </div>
        </div>

        <!-- Related Entities -->
        <div class="card mb-4">
            <div class="card-header">
                <h6><i class="bi bi-diagram-3"></i> Related Entities</h6>
            </div>
            <div class="card-body">
                <div id="related-entities">
                    <p class="text-muted text-center">Perform a search to see related entities</p>
                </div>
            </div>
        </div>

        <!-- Search History -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6><i class="bi bi-clock-history"></i> Recent Searches</h6>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearSearchHistory()">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="card-body">
                <div id="search-history">
                    <p class="text-muted text-center">No recent searches</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Result Detail Modal -->
<div class="modal fade" id="result-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Result Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="result-detail-content">
                <div class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Loading details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="view-source-btn">
                    <i class="bi bi-eye"></i> View Source
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentLimit = 10;
let searchResults = [];
let searchHistory = JSON.parse(localStorage.getItem('graphiti-search-history') || '[]');
let currentQuery = '';

document.addEventListener('DOMContentLoaded', function() {
    loadSearchHistory();
    loadFilterOptions();
    setupEventListeners();
    loadStats();
});

function setupEventListeners() {
    // Search input
    document.getElementById('search-query').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // Similarity threshold slider
    document.getElementById('similarity-threshold').addEventListener('input', function() {
        document.getElementById('similarity-value').textContent = this.value;
    });

    // Search type change
    document.getElementById('search-type').addEventListener('change', function() {
        if (currentQuery) {
            performSearch();
        }
    });
}

async function loadFilterOptions() {
    try {
        // Load documents for filter
        const docsResponse = await fetch('/api/documents?limit=100');
        if (docsResponse.ok) {
            const docsData = await docsResponse.json();
            const docSelect = document.getElementById('document-filter');
            docSelect.innerHTML = '<option value="">All Documents</option>';

            docsData.documents.forEach(doc => {
                const option = document.createElement('option');
                option.value = doc.uuid;
                option.textContent = doc.name || 'Untitled';
                docSelect.appendChild(option);
            });
        }

        // Load entity types
        const typesResponse = await fetch('/api/entities/types');
        if (typesResponse.ok) {
            const typesData = await typesResponse.json();
            const typeSelect = document.getElementById('entity-type-filter');
            typeSelect.innerHTML = '<option value="">All Types</option>';

            typesData.types.forEach(type => {
                const option = document.createElement('option');
                option.value = type.name;
                option.textContent = `${type.name} (${type.count})`;
                typeSelect.appendChild(option);
            });
        }

    } catch (error) {
        console.error('Error loading filter options:', error);
    }
}

async function loadStats() {
    try {
        const response = await fetch('/api/graph-stats');
        if (response.ok) {
            const stats = await response.json();
            document.getElementById('total-documents').textContent = stats.total_episodes || 0;
            document.getElementById('total-entities').textContent = stats.total_entities || 0;
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

async function performSearch() {
    const query = document.getElementById('search-query').value.trim();
    if (!query) {
        showAlert('Please enter a search query', 'warning');
        return;
    }

    currentQuery = query;
    currentPage = 1;

    try {
        showLoading();

        const searchType = document.getElementById('search-type').value;
        const documentFilter = document.getElementById('document-filter').value;
        const entityTypeFilter = document.getElementById('entity-type-filter').value;
        const dateRange = document.getElementById('date-range').value;
        const similarityThreshold = document.getElementById('similarity-threshold').value;

        const params = new URLSearchParams({
            q: query,
            limit: currentLimit,
            similarity_threshold: similarityThreshold
        });

        if (documentFilter) params.append('document_id', documentFilter);
        if (entityTypeFilter) params.append('entity_type', entityTypeFilter);
        if (dateRange) params.append('date_range', dateRange);

        const startTime = Date.now();
        let response;

        // Use appropriate search endpoint based on type
        if (searchType === 'semantic') {
            response = await fetch('/api/search/semantic', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: query,
                    top_k: currentLimit,
                    hybrid: false
                })
            });
        } else if (searchType === 'hybrid') {
            response = await fetch('/api/search/semantic', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: query,
                    top_k: currentLimit,
                    hybrid: true
                })
            });
        } else {
            response = await fetch(`/api/search/documents?${params}`);
        }

        const searchTime = Date.now() - startTime;

        if (!response.ok) throw new Error('Search failed');

        const data = await response.json();
        searchResults = data.results || [];

        displayResults(searchResults);
        updateSearchStats(data, searchTime);
        loadRelatedEntities(query);
        addToSearchHistory(query);

    } catch (error) {
        console.error('Error performing search:', error);
        showAlert('Search failed: ' + error.message, 'danger');
        showEmptyResults();
    }
}

function displayResults(results) {
    hideLoading();

    if (!results || results.length === 0) {
        showEmptyResults();
        return;
    }

    const container = document.getElementById('results-list');
    container.innerHTML = '';

    results.forEach((result, index) => {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'border-bottom p-3 fade-in';
        resultDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-2">
                        <a href="#" class="text-decoration-none" onclick="viewResultDetail('${result.id}')">
                            ${result.title || 'Untitled'}
                        </a>
                        <span class="badge bg-primary ms-2">${result.type || 'Document'}</span>
                    </h6>
                    <p class="text-muted mb-2">${result.content ? result.content.substring(0, 200) + '...' : 'No content preview'}</p>
                    <div class="d-flex align-items-center">
                        <small class="text-muted me-3">
                            <i class="bi bi-file-earmark-text"></i> ${result.source || 'Unknown source'}
                        </small>
                        ${result.entities ? `
                            <small class="text-muted me-3">
                                <i class="bi bi-diagram-3"></i> ${result.entities.length} entities
                            </small>
                        ` : ''}
                        <small class="text-muted">
                            <i class="bi bi-calendar"></i> ${result.date ? formatDate(result.date) : 'Unknown date'}
                        </small>
                    </div>
                </div>
                <div class="text-end">
                    <div class="mb-2">
                        <span class="badge bg-success">${Math.round((result.score || 0) * 100)}%</span>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewResultDetail('${result.id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="copyResult('${result.id}')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </div>
            ${result.entities && result.entities.length > 0 ? `
                <div class="mt-2">
                    <small class="text-muted">Related entities: </small>
                    ${result.entities.slice(0, 5).map(entity =>
                        `<span class="badge bg-secondary me-1">${entity.name}</span>`
                    ).join('')}
                    ${result.entities.length > 5 ? `<span class="text-muted">+${result.entities.length - 5} more</span>` : ''}
                </div>
            ` : ''}
        `;
        container.appendChild(resultDiv);
    });

    document.getElementById('results-container').style.display = 'block';
    document.getElementById('welcome-state').style.display = 'none';
    document.getElementById('empty-results').style.display = 'none';
    document.getElementById('results-pagination').style.display = 'block';
}

function updateSearchStats(data, searchTime) {
    document.getElementById('results-count').textContent = `${data.total_count || 0} results`;
    document.getElementById('search-time').textContent = searchTime;

    // Update pagination info
    document.getElementById('showing-start').textContent = ((currentPage - 1) * currentLimit) + 1;
    document.getElementById('showing-end').textContent = Math.min(currentPage * currentLimit, data.total_count || 0);
    document.getElementById('total-results').textContent = data.total_count || 0;
}

async function loadRelatedEntities(query) {
    try {
        const response = await fetch(`/api/search/entities?query=${encodeURIComponent(query)}&limit=5`);
        if (response.ok) {
            const data = await response.json();
            const entities = data.entities || [];

            const container = document.getElementById('related-entities');

            if (entities.length > 0) {
                container.innerHTML = entities.map(entity => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="badge bg-primary">${entity.type}</span>
                            <span class="ms-2">${entity.name}</span>
                        </div>
                        <small class="text-muted">${entity.mention_count}</small>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted text-center">No related entities found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading related entities:', error);
    }
}

function addToSearchHistory(query) {
    // Remove if already exists
    searchHistory = searchHistory.filter(item => item.query !== query);

    // Add to beginning
    searchHistory.unshift({
        query: query,
        timestamp: new Date().toISOString(),
        results_count: searchResults.length
    });

    // Keep only last 10
    searchHistory = searchHistory.slice(0, 10);

    // Save to localStorage
    localStorage.setItem('graphiti-search-history', JSON.stringify(searchHistory));

    // Update display
    loadSearchHistory();
}

function loadSearchHistory() {
    const container = document.getElementById('search-history');

    if (searchHistory.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No recent searches</p>';
        return;
    }

    container.innerHTML = searchHistory.map(item => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <div>
                <a href="#" class="text-decoration-none" onclick="setQuery('${item.query}')">
                    ${item.query}
                </a>
                <br>
                <small class="text-muted">${formatDate(item.timestamp)} • ${item.results_count} results</small>
            </div>
        </div>
    `).join('');
}

function setQuery(query) {
    document.getElementById('search-query').value = query;
    performSearch();
}

function setExampleQuery(query) {
    document.getElementById('search-query').value = query;
    document.getElementById('search-query').focus();
}

function toggleAdvancedSearch() {
    const panel = document.getElementById('advanced-search');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

function clearSearch() {
    document.getElementById('search-query').value = '';
    document.getElementById('results-container').style.display = 'none';
    document.getElementById('welcome-state').style.display = 'block';
    document.getElementById('empty-results').style.display = 'none';
    document.getElementById('results-pagination').style.display = 'none';
    document.getElementById('results-count').textContent = '0 results';
    document.getElementById('search-time').textContent = '-';
    document.getElementById('related-entities').innerHTML = '<p class="text-muted text-center">Perform a search to see related entities</p>';
    currentQuery = '';
    searchResults = [];
}

function clearSearchHistory() {
    if (confirm('Clear all search history?')) {
        searchHistory = [];
        localStorage.removeItem('graphiti-search-history');
        loadSearchHistory();
        showAlert('Search history cleared', 'success', 2000);
    }
}

async function viewResultDetail(resultId) {
    try {
        const result = searchResults.find(r => r.id === resultId);
        if (!result) throw new Error('Result not found');

        const modal = new bootstrap.Modal(document.getElementById('result-detail-modal'));
        modal.show();

        const content = document.getElementById('result-detail-content');
        content.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <h5>${result.title || 'Untitled'}</h5>
                    <div class="mb-3">
                        <span class="badge bg-primary">${result.type || 'Document'}</span>
                        <span class="badge bg-success ms-1">${Math.round((result.score || 0) * 100)}% match</span>
                    </div>

                    <h6>Content</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        ${result.content || 'No content available'}
                    </div>

                    <h6>Metadata</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Source:</strong></td><td>${result.source || 'Unknown'}</td></tr>
                        <tr><td><strong>Date:</strong></td><td>${result.date ? formatDate(result.date) : 'Unknown'}</td></tr>
                        <tr><td><strong>Type:</strong></td><td>${result.type || 'Unknown'}</td></tr>
                        <tr><td><strong>Score:</strong></td><td>${Math.round((result.score || 0) * 100)}%</td></tr>
                    </table>

                    ${result.entities && result.entities.length > 0 ? `
                        <h6>Related Entities</h6>
                        <div>
                            ${result.entities.map(entity =>
                                `<span class="badge bg-secondary me-1">${entity.name} (${entity.type})</span>`
                            ).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

    } catch (error) {
        console.error('Error viewing result detail:', error);
        showAlert('Error loading result details: ' + error.message, 'danger');
    }
}

async function copyResult(resultId) {
    try {
        const result = searchResults.find(r => r.id === resultId);
        if (!result) throw new Error('Result not found');

        const text = `${result.title || 'Untitled'}\n\n${result.content || 'No content'}\n\nSource: ${result.source || 'Unknown'}`;
        await navigator.clipboard.writeText(text);
        showAlert('Result copied to clipboard', 'success', 2000);

    } catch (error) {
        console.error('Error copying result:', error);
        showAlert('Error copying result: ' + error.message, 'danger');
    }
}

async function exportResults() {
    if (searchResults.length === 0) {
        showAlert('No results to export', 'warning');
        return;
    }

    try {
        const csvContent = [
            ['Title', 'Content', 'Type', 'Source', 'Score', 'Date'].join(','),
            ...searchResults.map(result => [
                `"${(result.title || '').replace(/"/g, '""')}"`,
                `"${(result.content || '').replace(/"/g, '""')}"`,
                `"${(result.type || '').replace(/"/g, '""')}"`,
                `"${(result.source || '').replace(/"/g, '""')}"`,
                result.score || 0,
                `"${result.date || ''}"`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `search-results-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        showAlert('Results exported successfully', 'success', 2000);

    } catch (error) {
        console.error('Error exporting results:', error);
        showAlert('Error exporting results: ' + error.message, 'danger');
    }
}

function showLoading() {
    document.getElementById('search-loading').style.display = 'block';
    document.getElementById('results-container').style.display = 'none';
    document.getElementById('welcome-state').style.display = 'none';
    document.getElementById('empty-results').style.display = 'none';
}

function hideLoading() {
    document.getElementById('search-loading').style.display = 'none';
}

function showEmptyResults() {
    hideLoading();
    document.getElementById('empty-results').style.display = 'block';
    document.getElementById('results-container').style.display = 'none';
    document.getElementById('welcome-state').style.display = 'none';
    document.getElementById('results-pagination').style.display = 'none';
}
</script>
{% endblock %}
