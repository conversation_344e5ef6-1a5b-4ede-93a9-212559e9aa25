// Knowledge Graph Visualization
let network = null;

function drawGraph(nodes, edges) {
    const container = document.getElementById('graph-container');
    
    // Create nodes with different colors based on type
    const nodeData = nodes.map(node => ({
        id: node.uuid,
        label: node.type === 'Episode' ? node.name.substring(0, 20) + '...' : node.name.substring(0, 30) + '...',
        title: node.type === 'Episode' ? node.name : node.body,
        color: node.type === 'Episode' ? '#4CAF50' : '#2196F3',
        font: { color: '#ffffff' },
        shape: node.type === 'Episode' ? 'box' : 'ellipse',
        size: node.type === 'Episode' ? 30 : 20,
        borderWidth: 2,
        shadow: true
    }));
    
    // Create edges
    const edgeData = edges.map(edge => ({
        from: edge.source,
        to: edge.target,
        label: edge.type,
        arrows: 'to',
        color: { color: '#999999' },
        font: { size: 10 }
    }));
    
    // Create a data set
    const data = {
        nodes: new vis.DataSet(nodeData),
        edges: new vis.DataSet(edgeData)
    };
    
    // Options for the network
    const options = {
        nodes: {
            shape: 'dot',
            size: 16
        },
        physics: {
            stabilization: true,
            barnesHut: {
                gravitationalConstant: -80000,
                springConstant: 0.001,
                springLength: 200
            }
        },
        layout: {
            improvedLayout: true,
            hierarchical: {
                enabled: false
            }
        },
        interaction: {
            navigationButtons: true,
            keyboard: true,
            tooltipDelay: 300,
            hover: true
        }
    };
    
    // Create the network
    network = new vis.Network(container, data, options);
    
    // Add event listeners
    network.on('click', function(params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            const node = nodes.find(n => n.uuid === nodeId);
            if (node) {
                alert(`${node.type}: ${node.name}\n\n${node.body ? node.body.substring(0, 500) + '...' : 'No content'}`); 
            }
        }
    });
}

function loadGraph() {
    document.getElementById('graph-loading').style.display = 'block';
    
    fetch('/api/graph-data')
        .then(response => response.json())
        .then(data => {
            document.getElementById('graph-loading').style.display = 'none';
            
            if (data.nodes.length === 0) {
                document.getElementById('graph-container').innerHTML = '<div class="alert alert-info">No graph data found.</div>';
                return;
            }
            
            drawGraph(data.nodes, data.relationships);
        })
        .catch(error => {
            document.getElementById('graph-loading').style.display = 'none';
            document.getElementById('graph-container').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        });
}

// Load graph when tab is clicked
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('graph-tab').addEventListener('click', function() {
        if (!network) {
            loadGraph();
        }
    });
    
    // Refresh graph button
    document.getElementById('refresh-graph').addEventListener('click', function() {
        loadGraph();
    });
});
