<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti Knowledge Graph Explorer</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Vis.js for Graph Visualization -->
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 12px 16px;
        }
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .score-badge {
            float: right;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
            height: 600px;
            border-radius: 8px;
        }
        .vis-network {
            outline: none;
        }
        #dropzone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        #dropzone.dragover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }
        .file-item {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 5px;
            padding: 8px 12px;
        }
        .reference-area {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
            padding: 10px 16px;
            border-radius: 4px 4px 0 0;
        }
        .nav-tabs .nav-link.active {
            font-weight: 500;
        }
        .metadata-card {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        .metadata-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }
        .metadata-value {
            margin-bottom: 10px;
        }
        /* Chat styles */
        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .user-message {
            background-color: #e9ecef;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .assistant-message {
            background-color: #f0f7ff;
            margin-right: auto;
            border-top-left-radius: 0;
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-content p {
            margin-bottom: 0.5rem;
        }
        .message-content p:last-child {
            margin-bottom: 0;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-header {
            text-align: right;
            color: #495057;
        }
        .assistant-header {
            color: #0d6efd;
        }
        .sources-toggle {
            margin-top: 5px;
            text-align: right;
        }
        .sources-container {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        #answer-loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        #answer-sources {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            max-height: 300px;
            overflow-y: auto;
        }
        /* Settings styles */
        .settings-section {
            margin-bottom: 30px;
        }
        .settings-title {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 15px;
            color: #212529;
        }
        .settings-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .settings-form-group {
            margin-bottom: 15px;
        }
        .settings-label {
            font-weight: 500;
            margin-bottom: 5px;
        }
        .settings-btn {
            margin-top: 10px;
        }
        /* Entity styles */
        .entity-type-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            background-color: #e9ecef;
            color: #495057;
            margin-right: 5px;
        }
        .entity-count-badge {
            font-size: 0.8rem;
            padding: 3px 7px;
            border-radius: 10px;
            background-color: #007bff;
            color: white;
        }
        /* Progress bar */
        .progress {
            height: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph Explorer</h1>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="chat-tab" data-bs-toggle="tab" data-bs-target="#chat" type="button" role="tab" aria-controls="chat" aria-selected="true">Chat</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="false">Search</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="entities-tab" data-bs-toggle="tab" data-bs-target="#entities" type="button" role="tab" aria-controls="entities" aria-selected="false">Entities</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="metadata-tab" data-bs-toggle="tab" data-bs-target="#metadata" type="button" role="tab" aria-controls="metadata" aria-selected="false">Metadata</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="references-tab" data-bs-toggle="tab" data-bs-target="#references" type="button" role="tab" aria-controls="references" aria-selected="false">References</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="enhancements-tab" data-bs-toggle="tab" data-bs-target="#enhancements" type="button" role="tab" aria-controls="enhancements" aria-selected="false">Enhancements</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="graph-tab" data-bs-toggle="tab" data-bs-target="#graph" type="button" role="tab" aria-controls="graph" aria-selected="false">Graph</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">Settings</button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabsContent">
            <!-- Chat Tab -->
            <div class="tab-pane fade show active" id="chat" role="tabpanel" aria-labelledby="chat-tab">
                <div class="row mb-4">
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="text" id="question-input" class="form-control" placeholder="Ask a question...">
                            <button class="btn btn-primary" type="button" id="question-button">Ask</button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" type="button" id="clear-chat-button">Clear Chat</button>
                    </div>
                </div>

                <div id="conversation-container" class="mb-4" style="max-height: 500px; overflow-y: auto;">
                    <div id="conversation-history"></div>
                </div>

                <div id="answer-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Generating answer...</p>
                </div>

                <div id="answer-sources" style="display: none;">
                    <h5>Sources:</h5>
                    <div id="sources-list"></div>
                </div>
            </div>

            <!-- Search Tab -->
            <div class="tab-pane fade" id="search" role="tabpanel" aria-labelledby="search-tab">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="input-group mb-3">
                            <input type="text" id="search-input" class="form-control" placeholder="Search the knowledge graph...">
                            <button class="btn btn-primary" type="button" id="search-button">Search</button>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="searchType" id="semanticSearch" value="semantic" checked>
                            <label class="form-check-label" for="semanticSearch">Semantic Search</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="searchType" id="hybridSearch" value="hybrid">
                            <label class="form-check-label" for="hybridSearch">Hybrid Search</label>
                        </div>
                    </div>
                </div>

                <div id="search-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Searching...</p>
                </div>

                <div id="search-results"></div>
            </div>

            <!-- Entities Tab -->
            <div class="tab-pane fade" id="entities" role="tabpanel" aria-labelledby="entities-tab">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group mb-3">
                                    <input type="text" id="entity-search-input" class="form-control" placeholder="Search entities...">
                                    <button class="btn btn-primary" type="button" id="entity-search-button">Search</button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select id="entity-type-filter" class="form-select mb-3">
                                    <option value="">All Types</option>
                                    <option value="Herb">Herb</option>
                                    <option value="Nutrient">Nutrient</option>
                                    <option value="Disease">Disease</option>
                                    <option value="Concept">Concept</option>
                                    <option value="Process">Process</option>
                                    <option value="Symptom">Symptom</option>
                                    <option value="Treatment">Treatment</option>
                                    <option value="Medication">Medication</option>
                                    <option value="Food">Food</option>
                                    <option value="Organization">Organization</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group mb-3">
                                    <span class="input-group-text">Min Mentions</span>
                                    <input type="number" id="min-mentions-filter" class="form-control" value="0" min="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="entities-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading entities...</p>
                </div>

                <div id="entities-list" class="row"></div>
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Upload PDF Documents</h5>
                    </div>
                    <div class="card-body">
                        <form id="upload-form" enctype="multipart/form-data">
                            <div id="dropzone">
                                <i class="bi bi-cloud-upload fs-1"></i>
                                <p>Drag and drop PDF files here or click to select files</p>
                                <input type="file" id="file-input" accept=".pdf" style="display: none;">
                                <button type="button" id="select-file-button" class="btn btn-outline-primary">Select File</button>
                            </div>
                            
                            <div id="selected-files" class="mt-3"></div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                                        <input type="number" class="form-control" id="chunk-size" name="chunk_size" value="1200" min="100" max="10000">
                                        <div class="form-text">Recommended: 1200 characters</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="overlap" class="form-label">Overlap (characters)</label>
                                        <input type="number" class="form-control" id="overlap" name="overlap" value="50" min="0" max="500">
                                        <div class="form-text">Recommended: 50 characters</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="extract-entities" name="extract_entities" checked>
                                        <label class="form-check-label" for="extract-entities">
                                            Extract Entities
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="extract-references" name="extract_references" checked>
                                        <label class="form-check-label" for="extract-references">
                                            Extract References
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="button" id="upload-button" class="btn btn-primary mt-3" disabled>Upload and Process</button>
                        </form>
                        
                        <div id="upload-progress" class="mt-4" style="display: none;">
                            <h5>Processing File...</h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <p id="upload-status">Uploading file...</p>
                        </div>
                        
                        <div id="upload-results" class="mt-4" style="display: none;">
                            <h5>Processing Results</h5>
                            <div id="upload-results-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Metadata Tab -->
            <div class="tab-pane fade" id="metadata" role="tabpanel" aria-labelledby="metadata-tab">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Document Metadata</h5>
                    </div>
                    <div class="card-body">
                        <div id="metadata-loading" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading metadata...</p>
                        </div>
                        
                        <div id="metadata-list"></div>
                    </div>
                </div>
            </div>

            <!-- References Tab -->
            <div class="tab-pane fade" id="references" role="tabpanel" aria-labelledby="references-tab">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Document References</h5>
                    </div>
                    <div class="card-body">
                        <div id="references-loading" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading references...</p>
                        </div>
                        
                        <div id="references-list"></div>
                    </div>
                </div>
            </div>

            <!-- Enhancements Tab -->
            <div class="tab-pane fade" id="enhancements" role="tabpanel" aria-labelledby="enhancements-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Reference Enhancements</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Deduplication</h5>
                                        <p class="card-text">Find and link duplicate references in the knowledge graph.</p>
                                        <button id="deduplicate-button" class="btn btn-primary">Run Deduplication</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Citation Network</h5>
                                        <p class="card-text">Build a citation network from references in the knowledge graph.</p>
                                        <button id="build-network-button" class="btn btn-primary">Build Network</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Bibliographic Enrichment</h5>
                                        <p class="card-text">Enrich references with data from external bibliographic databases.</p>
                                        <button id="enrich-references-button" class="btn btn-primary">Enrich References</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Run All</h5>
                                        <p class="card-text">Run all reference enhancements in sequence.</p>
                                        <button id="run-all-enhancements-button" class="btn btn-primary">Run All</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="enhancements-status">
                            <!-- Status will be displayed here -->
                        </div>

                        <div id="enhancements-progress" style="display: none;">
                            <h5 class="progress-message">Processing...</h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>

                        <div id="enhancements-results">
                            <!-- Results will be displayed here -->
                        </div>

                        <div id="network-visualization">
                            <!-- Network visualization will be displayed here -->
                        </div>

                        <div id="duplicate-groups-list">
                            <!-- Duplicate groups will be displayed here -->
                        </div>

                        <div id="enriched-references-list">
                            <!-- Enriched references will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graph Tab -->
            <div class="tab-pane fade" id="graph" role="tabpanel" aria-labelledby="graph-tab">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Knowledge Graph Visualization</h5>
                    </div>
                    <div class="card-body">
                        <div id="graph-container"></div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Application Settings</h5>
                    </div>
                    <div class="card-body">
                        <div id="settings-loading" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading settings...</p>
                        </div>
                        
                        <!-- LLM Settings -->
                        <div class="settings-section">
                            <h4 class="settings-title">LLM Settings</h4>
                            <div class="settings-card">
                                <form id="llm-settings-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="settings-form-group">
                                                <label for="llm-provider" class="settings-label">LLM Provider</label>
                                                <select id="llm-provider" class="form-select">
                                                    <option value="openrouter">OpenRouter</option>
                                                    <option value="local">Local (Ollama)</option>
                                                    <option value="gemini">Google Gemini</option>
                                                    <option value="openai">OpenAI</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="settings-form-group">
                                                <label for="llm-model" class="settings-label">LLM Model</label>
                                                <select id="llm-model" class="form-select">
                                                    <!-- Options will be populated dynamically -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" id="save-llm-settings" class="btn btn-primary settings-btn">Save LLM Settings</button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Embedding Settings -->
                        <div class="settings-section">
                            <h4 class="settings-title">Embedding Settings</h4>
                            <div class="settings-card">
                                <form id="embedding-settings-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="settings-form-group">
                                                <label for="embedding-provider" class="settings-label">Embedding Provider</label>
                                                <select id="embedding-provider" class="form-select">
                                                    <option value="openai">OpenAI</option>
                                                    <option value="local">Local</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="settings-form-group">
                                                <label for="embedding-model" class="settings-label">Embedding Model</label>
                                                <select id="embedding-model" class="form-select">
                                                    <!-- Options will be populated dynamically -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" id="save-embedding-settings" class="btn btn-primary settings-btn">Save Embedding Settings</button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Database Settings -->
                        <div class="settings-section">
                            <h4 class="settings-title">Database Settings</h4>
                            <div class="settings-card">
                                <form id="database-settings-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="settings-form-group">
                                                <label for="database-host" class="settings-label">FalkorDB Host</label>
                                                <input type="text" id="database-host" class="form-control" placeholder="localhost">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="settings-form-group">
                                                <label for="database-port" class="settings-label">FalkorDB Port</label>
                                                <input type="number" id="database-port" class="form-control" placeholder="6379">
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" id="save-database-settings" class="btn btn-primary settings-btn">Save Database Settings</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Showdown JS for Markdown conversion -->
    <script src="https://cdn.jsdelivr.net/npm/showdown@2.1.0/dist/showdown.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/flask_conversation.js"></script>
    <script src="/static/flask_search.js"></script>
    <script src="/static/flask_entities.js"></script>
    <script src="/static/flask_upload.js"></script>
    <script src="/static/flask_metadata.js"></script>
    <script src="/static/flask_references.js"></script>
    <script src="/static/flask_enhancements.js"></script>
    <script src="/static/flask_graph.js"></script>
    <script src="/static/flask_settings.js"></script>
</body>
</html>
