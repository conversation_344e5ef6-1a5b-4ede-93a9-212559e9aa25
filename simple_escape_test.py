#!/usr/bin/env python3
"""Simple test for string escaping without database dependencies."""

def escape_cypher_string(value):
    """Test version of the escape function."""
    if not isinstance(value, str):
        return str(value)
    
    # Remove non-printable characters except whitespace
    escaped = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
    
    # Replace problematic characters that can break Cypher syntax
    escaped = escaped.replace('\\', '\\\\')  # Escape backslashes first
    escaped = escaped.replace("'", "\\'")    # Escape single quotes with backslash
    escaped = escaped.replace('"', '\\"')    # Escape double quotes
    escaped = escaped.replace('\n', '\\n')   # Escape newlines
    escaped = escaped.replace('\r', '\\r')   # Escape carriage returns
    escaped = escaped.replace('\t', '\\t')   # Escape tabs
    
    # Remove or replace characters that can cause "multiple statement" errors
    escaped = escaped.replace(';', ',')      # Replace semicolons with commas
    escaped = escaped.replace('`', "'")      # Replace backticks with single quotes
    
    return escaped

# Test the problematic strings from the error logs
test_cases = [
    "the patient's story is paramount",
    "individual's mosaic and across",
    "e-Monitor'No. 31, May 2010",
    "2 to 18 weeks' duration",
    "text with; semicolon statement",
    "text with `backticks`",
    'text with "double quotes"'
]

print("Testing string escaping:")
print("=" * 60)

for i, test_str in enumerate(test_cases, 1):
    escaped = escape_cypher_string(test_str)
    print(f"{i}. Original: {test_str}")
    print(f"   Escaped:  {escaped}")
    is_safe = "'" not in escaped or "\\\\" in escaped
    print(f"   Safe:     {'✓' if is_safe else '✗'}")
    print()

print("Test completed.")
