{% extends "layouts/base.html" %}

{% block title %}Entities - Graphiti Knowledge Graph{% endblock %}

{% block breadcrumb_page %}Entities{% endblock %}

{% block additional_css %}
<style>
    .entity-type-badge {
        margin-right: 5px;
        margin-bottom: 5px;
        cursor: pointer;
    }
    .entity-type-badge.active {
        background-color: #007bff;
        color: white;
    }
    .entity-list {
        max-height: 600px;
        overflow-y: auto;
    }
    .entity-card {
        margin-bottom: 10px;
        cursor: pointer;
    }
    .entity-card:hover {
        background-color: #f8f9fa;
    }
    .entity-type-header {
        margin-top: 15px;
        margin-bottom: 10px;
        font-weight: bold;
    }
    .entity-count {
        font-size: 0.9em;
        color: #6c757d;
    }
    #entityTypeFilter {
        margin-bottom: 15px;
    }
    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <div class="row">
        <div class="col-md-12">
            <h2>Entities</h2>
            <p>Explore entities extracted from your documents.</p>
        </div>
    </div>

        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5>Entity Type</h5>
                    </div>
                    <div class="card-body">
                        <div id="entityTypeFilter">
                            <div class="form-group">
                                <input type="text" class="form-control" id="entityTypeSearch" placeholder="Search entity types...">
                            </div>
                            <div id="entityTypeBadges">
                                <!-- Entity type badges will be added here -->
                                <div class="entity-type-badge badge badge-primary active" data-type="All">All Types</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Filters</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="minMentions" class="form-label">Min Mentions</label>
                            <input type="number" class="form-control" id="minMentions" min="0" value="0">
                        </div>
                        <div class="form-group mb-3">
                            <label for="minConfidence" class="form-label">Min Confidence</label>
                            <input type="range" class="form-range" id="minConfidence" min="0" max="1" step="0.05" value="0">
                            <div class="d-flex justify-content-between">
                                <small>0</small>
                                <small id="confidenceValue">0</small>
                                <small>1</small>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="dateFilter" class="form-label">Date Range</label>
                            <select class="form-select" id="dateFilter">
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="year">This Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        <div id="customDateRange" class="form-group mb-3" style="display: none;">
                            <div class="row">
                                <div class="col-6">
                                    <label for="startDate" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="startDate">
                                </div>
                                <div class="col-6">
                                    <label for="endDate" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="endDate">
                                </div>
                            </div>
                        </div>
                        <button id="applyFilters" class="btn btn-primary">Apply Filters</button>
                        <button id="resetFilters" class="btn btn-outline-secondary">Reset</button>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5>Entity List</h5>
                            <div>
                                <input type="text" class="form-control" id="entitySearch" placeholder="Search entities...">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="entityList" class="entity-list">
                            <!-- Loading spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                            <!-- Error message -->
                            <div id="errorMessage" class="alert alert-danger" style="display: none;">
                                Error loading entities: <span id="errorText"></span>
                            </div>
                            <!-- Entity list will be added here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_js %}
<script src="/static/js/entities.js"></script>
{% endblock %}
