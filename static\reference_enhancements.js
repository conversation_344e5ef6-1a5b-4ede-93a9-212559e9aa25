/**
 * Reference enhancements functionality for the web interface
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const enhancementsTab = document.getElementById('enhancements-tab');
    const deduplicateButton = document.getElementById('deduplicate-button');
    const buildNetworkButton = document.getElementById('build-network-button');
    const enrichReferencesButton = document.getElementById('enrich-references-button');
    const runAllButton = document.getElementById('run-all-enhancements-button');
    const enhancementsStatus = document.getElementById('enhancements-status');
    const enhancementsProgress = document.getElementById('enhancements-progress');
    const enhancementsResults = document.getElementById('enhancements-results');
    const networkVisualization = document.getElementById('network-visualization');
    const duplicateGroupsList = document.getElementById('duplicate-groups-list');
    const enrichedReferencesList = document.getElementById('enriched-references-list');
    
    // Initialize
    if (enhancementsTab) {
        enhancementsTab.addEventListener('click', loadEnhancementsStatus);
    }
    
    // Deduplicate references
    if (deduplicateButton) {
        deduplicateButton.addEventListener('click', function() {
            runEnhancement('deduplicate');
        });
    }
    
    // Build citation network
    if (buildNetworkButton) {
        buildNetworkButton.addEventListener('click', function() {
            runEnhancement('build-network');
        });
    }
    
    // Enrich references
    if (enrichReferencesButton) {
        enrichReferencesButton.addEventListener('click', function() {
            runEnhancement('enrich');
        });
    }
    
    // Run all enhancements
    if (runAllButton) {
        runAllButton.addEventListener('click', function() {
            runEnhancement('run-all');
        });
    }
    
    // Load enhancements status
    async function loadEnhancementsStatus() {
        try {
            showStatus('Loading enhancements status...');
            
            const response = await fetch('/api/enhancements/status');
            const data = await response.json();
            
            if (response.ok) {
                updateEnhancementsStatus(data);
            } else {
                showError('Error loading enhancements status: ' + data.error);
            }
        } catch (error) {
            console.error('Error loading enhancements status:', error);
            showError('Error loading enhancements status: ' + error.message);
        }
    }
    
    // Update enhancements status
    function updateEnhancementsStatus(data) {
        if (!enhancementsStatus) return;
        
        // Clear status
        enhancementsStatus.innerHTML = '';
        
        // Create status card
        const card = document.createElement('div');
        card.className = 'card mb-3';
        
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        
        // Add status information
        const statusInfo = document.createElement('div');
        statusInfo.innerHTML = `
            <h5>Reference Enhancements Status</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6>Deduplication</h6>
                            <p><strong>Duplicate Groups:</strong> ${data.deduplication.duplicate_groups || 0}</p>
                            <p><strong>Duplicate References:</strong> ${data.deduplication.duplicate_references || 0}</p>
                            <p><strong>Last Run:</strong> ${formatDate(data.deduplication.last_run)}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6>Citation Network</h6>
                            <p><strong>Documents:</strong> ${data.citation_network.documents || 0}</p>
                            <p><strong>Citations:</strong> ${data.citation_network.citations || 0}</p>
                            <p><strong>Last Run:</strong> ${formatDate(data.citation_network.last_run)}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6>Bibliographic Enrichment</h6>
                            <p><strong>Enriched References:</strong> ${data.enrichment.enriched_references || 0}</p>
                            <p><strong>Last Run:</strong> ${formatDate(data.enrichment.last_run)}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        cardBody.appendChild(statusInfo);
        card.appendChild(cardBody);
        enhancementsStatus.appendChild(card);
        
        // Load visualization if available
        if (data.citation_network.visualization_url) {
            loadNetworkVisualization(data.citation_network.visualization_url);
        }
        
        // Load duplicate groups if available
        if (data.deduplication.duplicate_groups > 0) {
            loadDuplicateGroups();
        }
        
        // Load enriched references if available
        if (data.enrichment.enriched_references > 0) {
            loadEnrichedReferences();
        }
    }
    
    // Run enhancement
    async function runEnhancement(type) {
        try {
            // Disable buttons
            setButtonsEnabled(false);
            
            // Show progress
            showProgress('Starting enhancement...');
            
            // Start enhancement
            const response = await fetch(`/api/enhancements/${type}`, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (response.ok) {
                // Check if task is running in background
                if (data.task_id) {
                    // Poll for task status
                    pollTaskStatus(data.task_id);
                } else {
                    // Show results
                    showResults(data);
                    
                    // Reload status
                    loadEnhancementsStatus();
                    
                    // Enable buttons
                    setButtonsEnabled(true);
                }
            } else {
                showError('Error running enhancement: ' + data.error);
                setButtonsEnabled(true);
            }
        } catch (error) {
            console.error('Error running enhancement:', error);
            showError('Error running enhancement: ' + error.message);
            setButtonsEnabled(true);
        }
    }
    
    // Poll task status
    function pollTaskStatus(taskId) {
        const interval = setInterval(async function() {
            try {
                const response = await fetch(`/api/enhancements/task/${taskId}`);
                const data = await response.json();
                
                if (response.ok) {
                    // Update progress
                    if (data.status === 'running') {
                        showProgress(data.message, data.progress);
                    } else if (data.status === 'completed') {
                        // Clear interval
                        clearInterval(interval);
                        
                        // Show results
                        showResults(data.result);
                        
                        // Reload status
                        loadEnhancementsStatus();
                        
                        // Enable buttons
                        setButtonsEnabled(true);
                    } else if (data.status === 'failed') {
                        // Clear interval
                        clearInterval(interval);
                        
                        // Show error
                        showError('Enhancement failed: ' + data.error);
                        
                        // Enable buttons
                        setButtonsEnabled(true);
                    }
                } else {
                    // Clear interval
                    clearInterval(interval);
                    
                    // Show error
                    showError('Error checking task status: ' + data.error);
                    
                    // Enable buttons
                    setButtonsEnabled(true);
                }
            } catch (error) {
                console.error('Error checking task status:', error);
                // Don't clear interval, try again
            }
        }, 2000);
    }
    
    // Show status
    function showStatus(message) {
        if (!enhancementsStatus) return;
        
        enhancementsStatus.innerHTML = `
            <div class="alert alert-info">
                ${message}
            </div>
        `;
    }
    
    // Show progress
    function showProgress(message, progress = 0) {
        if (!enhancementsProgress) return;
        
        // Show progress container
        enhancementsProgress.style.display = 'block';
        
        // Update progress message
        const progressMessage = enhancementsProgress.querySelector('.progress-message');
        if (progressMessage) {
            progressMessage.textContent = message;
        }
        
        // Update progress bar
        const progressBar = enhancementsProgress.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
    }
    
    // Show results
    function showResults(data) {
        if (!enhancementsResults) return;
        
        // Hide progress
        if (enhancementsProgress) {
            enhancementsProgress.style.display = 'none';
        }
        
        // Clear results
        enhancementsResults.innerHTML = '';
        
        // Create results card
        const card = document.createElement('div');
        card.className = 'card mb-3';
        
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header';
        cardHeader.innerHTML = '<h5>Enhancement Results</h5>';
        
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        
        // Add results based on type
        if (data.type === 'deduplicate') {
            cardBody.innerHTML = `
                <h6>Deduplication Results</h6>
                <p><strong>Total References:</strong> ${data.total_references || 0}</p>
                <p><strong>Duplicate Groups:</strong> ${data.duplicate_groups || 0}</p>
                <p><strong>Duplicate References:</strong> ${data.duplicate_references || 0}</p>
                <p><strong>Relationships Created:</strong> ${data.relationships_created || 0}</p>
            `;
        } else if (data.type === 'build-network') {
            cardBody.innerHTML = `
                <h6>Citation Network Results</h6>
                <p><strong>Documents:</strong> ${data.documents || 0}</p>
                <p><strong>References:</strong> ${data.references || 0}</p>
                <p><strong>Citations:</strong> ${data.citations || 0}</p>
            `;
            
            if (data.statistics) {
                const stats = data.statistics;
                cardBody.innerHTML += `
                    <h6>Network Statistics</h6>
                    <p><strong>Nodes:</strong> ${stats.nodes || 0}</p>
                    <p><strong>Edges:</strong> ${stats.edges || 0}</p>
                    <p><strong>Connected Components:</strong> ${stats.connected_components || 0}</p>
                    <p><strong>Max In-Degree:</strong> ${stats.max_in_degree || 0}</p>
                `;
            }
            
            if (data.visualization_url) {
                loadNetworkVisualization(data.visualization_url);
            }
        } else if (data.type === 'enrich') {
            cardBody.innerHTML = `
                <h6>Bibliographic Enrichment Results</h6>
                <p><strong>Total References Processed:</strong> ${data.total_processed || 0}</p>
                <p><strong>Total References Enriched:</strong> ${data.total_enriched || 0}</p>
            `;
        } else if (data.type === 'run-all') {
            cardBody.innerHTML = '<h6>All Enhancements Completed</h6>';
            
            if (data.results) {
                if (data.results.deduplication) {
                    const dedup = data.results.deduplication;
                    cardBody.innerHTML += `
                        <h6>Deduplication Results</h6>
                        <p><strong>Total References:</strong> ${dedup.total_references || 0}</p>
                        <p><strong>Duplicate Groups:</strong> ${dedup.duplicate_groups || 0}</p>
                        <p><strong>Duplicate References:</strong> ${dedup.duplicate_references || 0}</p>
                    `;
                }
                
                if (data.results.citation_network) {
                    const network = data.results.citation_network;
                    cardBody.innerHTML += `
                        <h6>Citation Network Results</h6>
                        <p><strong>Documents:</strong> ${network.documents || 0}</p>
                        <p><strong>Citations:</strong> ${network.citations || 0}</p>
                    `;
                    
                    if (network.visualization_url) {
                        loadNetworkVisualization(network.visualization_url);
                    }
                }
                
                if (data.results.enrichment) {
                    const enrich = data.results.enrichment;
                    cardBody.innerHTML += `
                        <h6>Bibliographic Enrichment Results</h6>
                        <p><strong>Total References Processed:</strong> ${enrich.total_processed || 0}</p>
                        <p><strong>Total References Enriched:</strong> ${enrich.total_enriched || 0}</p>
                    `;
                }
            }
        }
        
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        enhancementsResults.appendChild(card);
    }
    
    // Show error
    function showError(message) {
        if (!enhancementsResults) return;
        
        // Hide progress
        if (enhancementsProgress) {
            enhancementsProgress.style.display = 'none';
        }
        
        // Show error
        enhancementsResults.innerHTML = `
            <div class="alert alert-danger">
                ${message}
            </div>
        `;
    }
    
    // Set buttons enabled/disabled
    function setButtonsEnabled(enabled) {
        if (deduplicateButton) deduplicateButton.disabled = !enabled;
        if (buildNetworkButton) buildNetworkButton.disabled = !enabled;
        if (enrichReferencesButton) enrichReferencesButton.disabled = !enabled;
        if (runAllButton) runAllButton.disabled = !enabled;
    }
    
    // Load network visualization
    function loadNetworkVisualization(url) {
        if (!networkVisualization) return;
        
        networkVisualization.innerHTML = `
            <div class="card mb-3">
                <div class="card-header">
                    <h5>Citation Network Visualization</h5>
                </div>
                <div class="card-body text-center">
                    <img src="${url}" class="img-fluid" alt="Citation Network Visualization">
                </div>
            </div>
        `;
    }
    
    // Load duplicate groups
    async function loadDuplicateGroups() {
        if (!duplicateGroupsList) return;
        
        try {
            const response = await fetch('/api/enhancements/duplicate-groups');
            const data = await response.json();
            
            if (response.ok) {
                // Clear list
                duplicateGroupsList.innerHTML = '';
                
                // Create card
                const card = document.createElement('div');
                card.className = 'card mb-3';
                
                const cardHeader = document.createElement('div');
                cardHeader.className = 'card-header';
                cardHeader.innerHTML = '<h5>Duplicate Groups</h5>';
                
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';
                
                if (data.groups.length === 0) {
                    cardBody.innerHTML = '<p>No duplicate groups found.</p>';
                } else {
                    // Create accordion for duplicate groups
                    const accordion = document.createElement('div');
                    accordion.className = 'accordion';
                    accordion.id = 'duplicateGroupsAccordion';
                    
                    // Add groups
                    data.groups.forEach((group, index) => {
                        const groupId = `duplicateGroup${index}`;
                        
                        const accordionItem = document.createElement('div');
                        accordionItem.className = 'accordion-item';
                        
                        const accordionHeader = document.createElement('h2');
                        accordionHeader.className = 'accordion-header';
                        
                        const accordionButton = document.createElement('button');
                        accordionButton.className = 'accordion-button collapsed';
                        accordionButton.type = 'button';
                        accordionButton.setAttribute('data-bs-toggle', 'collapse');
                        accordionButton.setAttribute('data-bs-target', `#${groupId}`);
                        accordionButton.setAttribute('aria-expanded', 'false');
                        accordionButton.setAttribute('aria-controls', groupId);
                        
                        // Use title or text for button label
                        const canonical = group.canonical;
                        const label = canonical.title || canonical.text || `Group ${index + 1}`;
                        accordionButton.textContent = `${label} (${group.duplicates.length + 1} references)`;
                        
                        accordionHeader.appendChild(accordionButton);
                        
                        const accordionCollapse = document.createElement('div');
                        accordionCollapse.id = groupId;
                        accordionCollapse.className = 'accordion-collapse collapse';
                        
                        const accordionBody = document.createElement('div');
                        accordionBody.className = 'accordion-body';
                        
                        // Add canonical reference
                        const canonicalCard = document.createElement('div');
                        canonicalCard.className = 'card bg-light mb-3';
                        
                        const canonicalCardHeader = document.createElement('div');
                        canonicalCardHeader.className = 'card-header';
                        canonicalCardHeader.innerHTML = '<strong>Canonical Reference</strong>';
                        
                        const canonicalCardBody = document.createElement('div');
                        canonicalCardBody.className = 'card-body';
                        
                        // Add reference details
                        canonicalCardBody.innerHTML = formatReference(canonical);
                        
                        canonicalCard.appendChild(canonicalCardHeader);
                        canonicalCard.appendChild(canonicalCardBody);
                        accordionBody.appendChild(canonicalCard);
                        
                        // Add duplicates
                        if (group.duplicates.length > 0) {
                            const duplicatesHeader = document.createElement('h6');
                            duplicatesHeader.className = 'mt-3';
                            duplicatesHeader.textContent = 'Duplicates';
                            accordionBody.appendChild(duplicatesHeader);
                            
                            group.duplicates.forEach(duplicate => {
                                const duplicateCard = document.createElement('div');
                                duplicateCard.className = 'card mb-2';
                                
                                const duplicateCardBody = document.createElement('div');
                                duplicateCardBody.className = 'card-body';
                                
                                // Add reference details
                                duplicateCardBody.innerHTML = formatReference(duplicate);
                                
                                duplicateCard.appendChild(duplicateCardBody);
                                accordionBody.appendChild(duplicateCard);
                            });
                        }
                        
                        accordionCollapse.appendChild(accordionBody);
                        
                        accordionItem.appendChild(accordionHeader);
                        accordionItem.appendChild(accordionCollapse);
                        
                        accordion.appendChild(accordionItem);
                    });
                    
                    cardBody.appendChild(accordion);
                }
                
                card.appendChild(cardHeader);
                card.appendChild(cardBody);
                duplicateGroupsList.appendChild(card);
            } else {
                duplicateGroupsList.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading duplicate groups: ${data.error}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading duplicate groups:', error);
            duplicateGroupsList.innerHTML = `
                <div class="alert alert-danger">
                    Error loading duplicate groups: ${error.message}
                </div>
            `;
        }
    }
    
    // Load enriched references
    async function loadEnrichedReferences() {
        if (!enrichedReferencesList) return;
        
        try {
            const response = await fetch('/api/enhancements/enriched-references');
            const data = await response.json();
            
            if (response.ok) {
                // Clear list
                enrichedReferencesList.innerHTML = '';
                
                // Create card
                const card = document.createElement('div');
                card.className = 'card mb-3';
                
                const cardHeader = document.createElement('div');
                cardHeader.className = 'card-header d-flex justify-content-between align-items-center';
                
                const headerTitle = document.createElement('h5');
                headerTitle.textContent = 'Enriched References';
                
                const downloadButton = document.createElement('button');
                downloadButton.className = 'btn btn-sm btn-outline-primary';
                downloadButton.innerHTML = '<i class="bi bi-download"></i> Download CSV';
                downloadButton.addEventListener('click', function() {
                    window.location.href = '/api/enhancements/enriched-references-csv';
                });
                
                cardHeader.appendChild(headerTitle);
                cardHeader.appendChild(downloadButton);
                
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';
                
                if (data.references.length === 0) {
                    cardBody.innerHTML = '<p>No enriched references found.</p>';
                } else {
                    // Create table
                    const table = document.createElement('table');
                    table.className = 'table table-striped table-hover';
                    
                    // Create table header
                    const thead = document.createElement('thead');
                    const headerRow = document.createElement('tr');
                    
                    ['Title', 'Authors', 'Year', 'Journal', 'DOI', 'Sources'].forEach(column => {
                        const th = document.createElement('th');
                        th.textContent = column;
                        headerRow.appendChild(th);
                    });
                    
                    thead.appendChild(headerRow);
                    table.appendChild(thead);
                    
                    // Create table body
                    const tbody = document.createElement('tbody');
                    
                    data.references.forEach(reference => {
                        const row = document.createElement('tr');
                        
                        // Title cell
                        const titleCell = document.createElement('td');
                        titleCell.textContent = reference.title || '';
                        row.appendChild(titleCell);
                        
                        // Authors cell
                        const authorsCell = document.createElement('td');
                        authorsCell.textContent = reference.authors || '';
                        row.appendChild(authorsCell);
                        
                        // Year cell
                        const yearCell = document.createElement('td');
                        yearCell.textContent = reference.year || '';
                        row.appendChild(yearCell);
                        
                        // Journal cell
                        const journalCell = document.createElement('td');
                        journalCell.textContent = reference.journal || '';
                        row.appendChild(journalCell);
                        
                        // DOI cell
                        const doiCell = document.createElement('td');
                        if (reference.doi) {
                            const doiLink = document.createElement('a');
                            doiLink.href = reference.doi.startsWith('http') ? reference.doi : `https://doi.org/${reference.doi}`;
                            doiLink.textContent = reference.doi;
                            doiLink.target = '_blank';
                            doiCell.appendChild(doiLink);
                        }
                        row.appendChild(doiCell);
                        
                        // Sources cell
                        const sourcesCell = document.createElement('td');
                        if (reference.enriched_from) {
                            const sources = Array.isArray(reference.enriched_from) ? reference.enriched_from : [reference.enriched_from];
                            sourcesCell.textContent = sources.join(', ');
                        }
                        row.appendChild(sourcesCell);
                        
                        tbody.appendChild(row);
                    });
                    
                    table.appendChild(tbody);
                    cardBody.appendChild(table);
                }
                
                card.appendChild(cardHeader);
                card.appendChild(cardBody);
                enrichedReferencesList.appendChild(card);
            } else {
                enrichedReferencesList.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading enriched references: ${data.error}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading enriched references:', error);
            enrichedReferencesList.innerHTML = `
                <div class="alert alert-danger">
                    Error loading enriched references: ${error.message}
                </div>
            `;
        }
    }
    
    // Format reference
    function formatReference(reference) {
        let html = '';
        
        if (reference.title) {
            html += `<p><strong>Title:</strong> ${reference.title}</p>`;
        }
        
        if (reference.authors) {
            html += `<p><strong>Authors: <AUTHORS>
        }
        
        if (reference.year) {
            html += `<p><strong>Year:</strong> ${reference.year}</p>`;
        }
        
        if (reference.journal) {
            html += `<p><strong>Journal:</strong> ${reference.journal}</p>`;
        }
        
        if (reference.doi) {
            html += `<p><strong>DOI:</strong> <a href="https://doi.org/${reference.doi}" target="_blank">${reference.doi}</a></p>`;
        }
        
        if (reference.text && !reference.title) {
            html += `<p><strong>Text:</strong> ${reference.text}</p>`;
        }
        
        return html;
    }
    
    // Format date
    function formatDate(dateString) {
        if (!dateString) return 'Never';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleString();
        } catch (error) {
            return dateString;
        }
    }
});
