/**
 * Conversation handling for Graphiti Flask Web Interface
 *
 * This script handles the Q&A functionality in the Chat tab.
 */

// Initialize conversation history
let conversationHistory = [];

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Flask Conversation.js loaded");

    // Add welcome message
    addMessage('assistant', 'Hello! I am <PERSON>, your AI assistant. Ask me a question about natural medicine and health.');

    // Add event listener for the question button
    document.getElementById('question-button').addEventListener('click', function() {
        askQuestion();
    });

    // Add event listener for Enter key in the input field
    document.getElementById('question-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            askQuestion();
        }
    });

    // Add event listener for the clear chat button
    document.getElementById('clear-chat-button').addEventListener('click', function() {
        clearChat();
    });
});

/**
 * Ask a question and get an answer
 */
function askQuestion() {
    // Get the question from the input field
    const questionInput = document.getElementById('question-input');
    const question = questionInput.value.trim();

    // If the question is empty, do nothing
    if (!question) {
        return;
    }

    // Add the user's question to the conversation
    addMessage('user', question);

    // Clear the input field
    questionInput.value = '';

    // Show the loading indicator
    document.getElementById('answer-loading').style.display = 'block';

    // Hide the sources
    document.getElementById('answer-sources').style.display = 'none';

    // Build the context from conversation history
    let context = '';
    if (conversationHistory.length > 0) {
        // Get the last 3 exchanges (6 messages) for context
        const recentHistory = conversationHistory.slice(-6);
        for (const msg of recentHistory) {
            context += `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}\n`;
        }
    }

    // Call the API to get an answer
    fetch('/api/qa/answer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            question: question,
            context: context
        })
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide the loading indicator
            document.getElementById('answer-loading').style.display = 'none';

            // Log the response data for debugging
            console.log("Response data:", data);

            // Add the answer to the conversation
            if (data.answer) {
                addMessage('assistant', data.answer);

                // Display sources if available
                if (data.sources && data.sources.length > 0) {
                    console.log("Sources found:", data.sources);
                    displaySources(data.sources);
                } else {
                    console.log("No sources found in response");
                }
            } else {
                console.log("No answer found in response");
                addMessage('assistant', "I'm sorry, but I couldn't find an answer to your question.");
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide the loading indicator
            document.getElementById('answer-loading').style.display = 'none';

            // Add an error message to the conversation
            addMessage('assistant', `I'm sorry, but there was an error processing your question: ${error.message}`);

            // Log detailed error information
            console.error('Detailed error:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });
        });
}

/**
 * Add a message to the conversation
 *
 * @param {string} role - The role of the message sender ('user' or 'assistant')
 * @param {string} content - The content of the message
 */
function addMessage(role, content) {
    // Create the message element
    const messageElement = document.createElement('div');
    messageElement.className = `message ${role}-message`;

    // Create the message content element
    const contentElement = document.createElement('div');
    contentElement.className = 'message-content';

    // Convert markdown to HTML if showdown is available
    if (window.showdown) {
        const converter = new showdown.Converter({
            tables: true,
            simplifiedAutoLink: true,
            strikethrough: true,
            tasklists: true
        });
        contentElement.innerHTML = converter.makeHtml(content);
    } else {
        // Basic formatting if showdown is not available
        contentElement.innerHTML = content.replace(/\n/g, '<br>');
    }

    // Add the content to the message
    messageElement.appendChild(contentElement);

    // Add the message to the conversation
    document.getElementById('conversation-history').appendChild(messageElement);

    // Scroll to the bottom of the conversation
    const conversationContainer = document.getElementById('conversation-container');
    conversationContainer.scrollTop = conversationContainer.scrollHeight;

    // Add the message to the conversation history
    conversationHistory.push({ role, content });
}

/**
 * Display sources for the answer
 *
 * @param {Array} sources - The sources for the answer
 */
function displaySources(sources) {
    // Get the sources list element
    const sourcesList = document.getElementById('sources-list');

    // Clear the sources list
    sourcesList.innerHTML = '';

    // Add a header with better styling
    const headerElement = document.createElement('h5');
    headerElement.className = 'mb-3 mt-2 text-primary';
    headerElement.innerHTML = '<i class="fas fa-book"></i> References';
    sourcesList.appendChild(headerElement);

    // Add a description
    const descriptionElement = document.createElement('p');
    descriptionElement.className = 'text-muted small mb-3';
    descriptionElement.textContent = 'The following sources were used to generate the answer:';
    sourcesList.appendChild(descriptionElement);

    // Add each source to the list
    sources.forEach((source, index) => {
        const sourceElement = document.createElement('div');
        sourceElement.className = 'card mb-3 border-light shadow-sm';

        const sourceBody = document.createElement('div');
        sourceBody.className = 'card-body py-2 px-3';

        const sourceTitle = document.createElement('h6');
        sourceTitle.className = 'card-title mb-1 text-primary';

        // Use the document title as is - it's already formatted by the backend
        let documentTitle = source.document || 'Unknown Source';
        sourceTitle.textContent = documentTitle;

        // Add document metadata
        const metadataElement = document.createElement('div');
        metadataElement.className = 'source-metadata small text-muted mb-2';

        // Add document ID if available
        if (source.document_id) {
            const docIdElement = document.createElement('span');
            docIdElement.className = 'badge bg-secondary me-2';
            docIdElement.textContent = `DocID: ${source.document_id}`;
            metadataElement.appendChild(docIdElement);
        }

        // Add document section/chunk if available
        if (source.chunk_num) {
            const chunkElement = document.createElement('span');
            chunkElement.className = 'badge bg-info me-2';
            chunkElement.textContent = `Section: ${source.chunk_num}`;
            metadataElement.appendChild(chunkElement);
        }

        const sourceText = document.createElement('p');
        sourceText.className = 'card-text small text-muted';

        // Use the preview text as is - it's already cleaned by the backend
        let previewText = source.preview || 'No preview available';
        sourceText.textContent = previewText;

        sourceBody.appendChild(sourceTitle);
        sourceBody.appendChild(metadataElement);
        sourceBody.appendChild(sourceText);
        sourceElement.appendChild(sourceBody);

        sourcesList.appendChild(sourceElement);
    });

    // Show the sources
    document.getElementById('answer-sources').style.display = 'block';
}

/**
 * Clear the chat
 */
function clearChat() {
    // Clear the conversation history
    conversationHistory = [];

    // Clear the conversation display
    document.getElementById('conversation-history').innerHTML = '';

    // Clear the sources
    document.getElementById('sources-list').innerHTML = '';
    document.getElementById('answer-sources').style.display = 'none';

    // Add the welcome message back
    addMessage('assistant', 'Hello! I am Avery, your AI assistant. Ask me a question about natural medicine and health.');
}
