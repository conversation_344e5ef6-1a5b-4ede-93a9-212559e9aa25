/**
 * Settings handling for Graphiti Flask Web Interface
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("Settings.js loaded");

    // Debug: Log the initial state of the embedding model dropdown
    const embeddingModelSelect = document.getElementById('embedding-model');
    if (embeddingModelSelect) {
        console.log("Initial embedding model options:", Array.from(embeddingModelSelect.options).map(opt => opt.value));
    }

    // DOM elements
    const settingsTab = document.getElementById('settings-tab');
    const settingsLoading = document.getElementById('settings-loading');

    // LLM settings elements
    const llmProvider = document.getElementById('llm-provider');
    const llmModel = document.getElementById('llm-model');
    const saveLlmSettingsBtn = document.getElementById('save-llm-settings');

    // Embedding settings elements
    const embeddingProvider = document.getElementById('embedding-provider');
    const embeddingModel = document.getElementById('embedding-model');
    const chunkSize = document.getElementById('chunk-size');
    const chunkOverlap = document.getElementById('chunk-overlap');
    const recursiveChunking = document.getElementById('recursive-chunking');
    const saveEmbeddingSettingsBtn = document.getElementById('save-embedding-settings');

    // Database settings elements
    const databaseHost = document.getElementById('database-host');
    const databasePort = document.getElementById('database-port');
    const saveDatabaseSettingsBtn = document.getElementById('save-database-settings');

    // Default settings
    const defaultSettings = {
        llm: {
            provider: 'openrouter',
            model: 'meta-llama/llama-4-maverick'
        },
        embedding: {
            provider: 'local',
            model: 'snowflake-arctic-embed2:latest',
            chunk_size: 1200,
            chunk_overlap: 0,
            recursive_chunking: true,
            use_local: true
        },
        database: {
            host: 'localhost',
            port: 6379
        },
        available_models: {
            openrouter: [
                'meta-llama/llama-4-maverick',
                'anthropic/claude-3-opus-20240229',
                'anthropic/claude-3-sonnet-20240229',
                'google/gemini-1.5-pro-latest',
                'mistralai/mistral-large-latest',
                'qwen3-4b',
                'qwen3-30b-a3b',
                'google/gemma-3-27b-it',
                'mistralai/mistral-nemo',
                'huggingfaceh4/zephyr-7b-beta',
                'nvidia/llama-3.3-nemotron-super-49b-v1:free'
            ],
            ollama: ['medllama3-v20', 'llama3-8b', 'mistral-7b', 'gemma-7b'],
            gemini: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-1.0-pro'],
            openai: ['gpt-4o', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o-mini'],
            mistral: ['mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest']
        }
    };

    // Initialize settings
    if (settingsTab) {
        settingsTab.addEventListener('click', loadSettings);
    }

    // Initialize event listeners
    if (llmProvider) {
        llmProvider.addEventListener('change', updateLlmModelOptions);
    }

    if (embeddingProvider) {
        // Don't automatically update embedding model options when provider changes
        // This preserves the HTML options

        // Set default provider to local for Ollama
        if (embeddingProvider.value !== 'local') {
            embeddingProvider.value = 'local';
        }

        // Force select Snowflake model
        forceSelectSnowflakeModel();
    }

    // Function to force select the Snowflake model
    function forceSelectSnowflakeModel() {
        if (embeddingModel) {
            const targetModel = 'snowflake-arctic-embed2:latest';
            console.log("Forcing selection of model:", targetModel);

            // Log all available options
            console.log("Available options:", Array.from(embeddingModel.options).map(opt => opt.value));

            // Try to find and select the Snowflake model
            for (let i = 0; i < embeddingModel.options.length; i++) {
                if (embeddingModel.options[i].value === targetModel) {
                    console.log("Found and selecting model:", targetModel);
                    embeddingModel.selectedIndex = i;
                    break;
                }
            }
        }
    }

    if (saveLlmSettingsBtn) {
        saveLlmSettingsBtn.addEventListener('click', saveLlmSettings);
    }

    if (saveEmbeddingSettingsBtn) {
        saveEmbeddingSettingsBtn.addEventListener('click', saveEmbeddingSettings);
    }

    if (saveDatabaseSettingsBtn) {
        saveDatabaseSettingsBtn.addEventListener('click', saveDatabaseSettings);
    }

    /**
     * Load settings from the server
     */
    function loadSettings() {
        console.log("Loading settings...");

        if (settingsLoading) {
            settingsLoading.style.display = 'block';
        }

        fetch('/api/settings')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Settings data received:", data);

                // Create a deep copy of the data to avoid modifying the original
                const settingsData = JSON.parse(JSON.stringify(data));

                // Ensure embedding provider is set to local and model to snowflake
                if (settingsData && settingsData.embedding) {
                    settingsData.embedding.provider = 'local';
                    settingsData.embedding.model = 'snowflake-arctic-embed2:latest';
                }

                // Make sure we have available models
                if (!settingsData.available_models || !settingsData.available_models.openrouter || !Array.isArray(settingsData.available_models.openrouter)) {
                    console.warn("No available models found in settings data, using defaults");
                    settingsData.available_models = defaultSettings.available_models;
                } else {
                    console.log("Available models found:", settingsData.available_models);

                    // Ensure we have all the default models in the list
                    if (settingsData.available_models.openrouter) {
                        const defaultOpenRouterModels = defaultSettings.available_models.openrouter;
                        for (const model of defaultOpenRouterModels) {
                            if (!settingsData.available_models.openrouter.includes(model)) {
                                console.log(`Adding missing default model: ${model}`);
                                settingsData.available_models.openrouter.push(model);
                            }
                        }
                    }
                }

                // Apply settings with fallbacks to defaults, but don't update embedding model options
                applySettingsWithoutEmbeddingModelUpdate(settingsData || defaultSettings);

                // Set provider to local but don't update the model options
                if (embeddingProvider) {
                    embeddingProvider.value = 'local';

                    // Force select Snowflake model
                    forceSelectSnowflakeModel();
                }

                if (settingsLoading) {
                    settingsLoading.style.display = 'none';
                }
            })
            .catch(error => {
                console.error("Error loading settings:", error);

                // Apply default settings on error, but don't update embedding model options
                applySettingsWithoutEmbeddingModelUpdate(defaultSettings);

                // Set provider to local but don't update the model options
                if (embeddingProvider) {
                    embeddingProvider.value = 'local';

                    // Force select Snowflake model
                    forceSelectSnowflakeModel();
                }

                if (settingsLoading) {
                    settingsLoading.style.display = 'none';
                }

                alert(`Error loading settings: ${error.message}. Using default settings.`);
            });
    }

    /**
     * Apply settings to the UI
     */
    function applySettings(data) {
        try {
            // Apply LLM settings
            if (llmProvider && data.llm && data.llm.provider) {
                llmProvider.value = data.llm.provider;
            }

            // Update LLM model options
            const availableModels = data.available_models || defaultSettings.available_models;
            const currentLlmModel = data.llm && data.llm.model ? data.llm.model : defaultSettings.llm.model;
            updateLlmModelOptionsWithData(availableModels, currentLlmModel);

            // Apply embedding settings
            if (embeddingProvider && data.embedding && data.embedding.provider) {
                embeddingProvider.value = data.embedding.provider;
            }

            // DISABLED - Do not update embedding model options
            console.log("Skipping embedding model options update in applySettings");

            // Force select Snowflake model
            forceSelectSnowflakeModel();

            // Apply chunking settings
            if (chunkSize && data.embedding) {
                chunkSize.value = data.embedding.chunk_size || defaultSettings.embedding.chunk_size;
            }

            if (chunkOverlap && data.embedding) {
                chunkOverlap.value = data.embedding.chunk_overlap || defaultSettings.embedding.chunk_overlap;
            }

            if (recursiveChunking && data.embedding) {
                const isRecursive = data.embedding.recursive_chunking === true ||
                    data.embedding.recursive_chunking === 'true';
                recursiveChunking.value = isRecursive ? 'true' : 'false';
            }

            // Apply database settings
            if (databaseHost && data.database) {
                databaseHost.value = data.database.host || defaultSettings.database.host;
            }

            if (databasePort && data.database) {
                databasePort.value = data.database.port || defaultSettings.database.port;
            }
        } catch (error) {
            console.error("Error applying settings:", error);
        }
    }

    /**
     * Apply settings to the UI without updating embedding model options
     */
    function applySettingsWithoutEmbeddingModelUpdate(data) {
        try {
            // Apply LLM settings
            if (llmProvider && data.llm && data.llm.provider) {
                llmProvider.value = data.llm.provider;
            }

            // Update LLM model options
            const availableModels = data.available_models || defaultSettings.available_models;
            const currentLlmModel = data.llm && data.llm.model ? data.llm.model : defaultSettings.llm.model;
            updateLlmModelOptionsWithData(availableModels, currentLlmModel);

            // Apply embedding settings
            if (embeddingProvider && data.embedding && data.embedding.provider) {
                embeddingProvider.value = data.embedding.provider;
            }

            // DO NOT update embedding model options - preserve the HTML options

            // Force select Snowflake model
            forceSelectSnowflakeModel();

            // Apply chunking settings
            if (chunkSize && data.embedding) {
                chunkSize.value = data.embedding.chunk_size || defaultSettings.embedding.chunk_size;
            }

            if (chunkOverlap && data.embedding) {
                chunkOverlap.value = data.embedding.chunk_overlap || defaultSettings.embedding.chunk_overlap;
            }

            if (recursiveChunking && data.embedding) {
                const isRecursive = data.embedding.recursive_chunking === true ||
                    data.embedding.recursive_chunking === 'true';
                recursiveChunking.value = isRecursive ? 'true' : 'false';
            }

            // Apply database settings
            if (databaseHost && data.database) {
                databaseHost.value = data.database.host || defaultSettings.database.host;
            }

            if (databasePort && data.database) {
                databasePort.value = data.database.port || defaultSettings.database.port;
            }
        } catch (error) {
            console.error("Error applying settings:", error);
        }
    }

    /**
     * Update LLM model options based on the selected provider
     */
    function updateLlmModelOptions() {
        console.log("updateLlmModelOptions called, fetching fresh data from API");

        // Fetch fresh data from API to get latest models
        fetch('/api/settings')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                const availableModels = data.available_models || defaultSettings.available_models;
                updateLlmModelOptionsWithData(availableModels, null);
            })
            .catch(error => {
                console.error('Error fetching models for provider change:', error);
                // Fallback to default settings
                const availableModels = defaultSettings.available_models;
                updateLlmModelOptionsWithData(availableModels, null);
            });
    }

    /**
     * Update LLM model options with the provided data
     */
    function updateLlmModelOptionsWithData(availableModels, currentModel) {
        if (!llmModel) {
            console.error("LLM model select element not found");
            return;
        }

        const provider = llmProvider ? llmProvider.value : 'openrouter';
        console.log("Updating LLM model options for provider:", provider);
        console.log("Available models data:", availableModels);

        // Clear current options
        llmModel.innerHTML = '';

        // Get models for the selected provider
        let modelsToAdd = [];

        // First try to get models from the provided data
        if (provider in availableModels && Array.isArray(availableModels[provider])) {
            console.log(`Found ${availableModels[provider].length} models for provider ${provider} in API data`);
            modelsToAdd = availableModels[provider];
        }
        // If no models found or not an array, use default models
        else {
            console.warn(`No models found for provider ${provider} in API data, using defaults`);
            modelsToAdd = defaultSettings.available_models[provider] || [
                'meta-llama/llama-4-maverick',
                'anthropic/claude-3-opus-20240229',
                'anthropic/claude-3-sonnet-20240229'
            ];
        }

        // Add the models to the dropdown
        console.log(`Adding ${modelsToAdd.length} models to dropdown`);
        modelsToAdd.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            llmModel.appendChild(option);
        });

        // Set current model if provided
        if (currentModel) {
            console.log("Setting current model to:", currentModel);
            let modelFound = false;

            // If the current model exists in the options, select it
            for (let i = 0; i < llmModel.options.length; i++) {
                if (llmModel.options[i].value === currentModel) {
                    llmModel.selectedIndex = i;
                    modelFound = true;
                    console.log("Found and selected model:", currentModel);
                    break;
                }
            }

            // If model not found, add it and select it
            if (!modelFound) {
                console.log("Model not found in options, adding it:", currentModel);
                const option = document.createElement('option');
                option.value = currentModel;
                option.textContent = currentModel;
                llmModel.appendChild(option);
                llmModel.value = currentModel;
            }
        } else if (llmModel.options.length > 0) {
            // If no current model provided, select the first option
            console.log("No current model provided, selecting first option:", llmModel.options[0].value);
            llmModel.selectedIndex = 0;
        }
    }

    /**
     * Update embedding model options based on the selected provider
     * DISABLED - Using HTML options instead
     */
    function updateEmbeddingModelOptions() {
        console.log("updateEmbeddingModelOptions called but DISABLED");
        // DISABLED - Using HTML options instead

        // Log current options for debugging
        if (embeddingModel) {
            console.log("Current embedding model options:", Array.from(embeddingModel.options).map(opt => opt.value));
        }
    }

    /**
     * Update embedding model options with the provided data
     * DISABLED - Using HTML options instead
     */
    function updateEmbeddingModelOptionsWithData(embeddingModels, currentModel) {
        console.log("updateEmbeddingModelOptionsWithData called but DISABLED");
        // DISABLED - Using HTML options instead

        // Only select the Snowflake model if it exists
        if (embeddingModel && currentModel) {
            console.log("Trying to select model:", currentModel);
            // If the current model exists in the options, select it
            for (let i = 0; i < embeddingModel.options.length; i++) {
                if (embeddingModel.options[i].value === currentModel) {
                    console.log("Found and selecting model:", currentModel);
                    embeddingModel.selectedIndex = i;
                    break;
                }
            }
        }
    }

    /**
     * Save LLM settings
     */
    function saveLlmSettings() {
        if (!llmProvider || !llmModel) return;

        const provider = llmProvider.value;
        const model = llmModel.value;
        const useLocal = provider === 'ollama';

        if (!provider || !model) {
            alert('Please select a provider and model.');
            return;
        }

        if (settingsLoading) {
            settingsLoading.style.display = 'block';
        }

        fetch('/api/settings/llm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                provider: provider,
                model: model,
                use_local: useLocal
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (settingsLoading) {
                settingsLoading.style.display = 'none';
            }

            alert('LLM settings saved successfully.');
        })
        .catch(error => {
            console.error('Error saving LLM settings:', error);

            if (settingsLoading) {
                settingsLoading.style.display = 'none';
            }

            alert(`Error saving LLM settings: ${error.message}`);
        });
    }

    /**
     * Save embedding settings
     */
    function saveEmbeddingSettings() {
        if (!embeddingProvider || !embeddingModel || !chunkSize || !chunkOverlap || !recursiveChunking) return;

        const provider = 'local';
        const model = 'snowflake-arctic-embed2:latest';
        const chunkSizeValue = chunkSize.value;
        const chunkOverlapValue = chunkOverlap.value;
        const recursiveChunkingValue = recursiveChunking.value;

        console.log("Saving embedding settings with model:", model);

        if (!provider || !model) {
            alert('Please select a provider and model.');
            return;
        }

        if (!chunkSizeValue || isNaN(parseInt(chunkSizeValue)) || parseInt(chunkSizeValue) <= 0) {
            alert('Please enter a valid chunk size (positive number).');
            return;
        }

        if (!chunkOverlapValue || isNaN(parseInt(chunkOverlapValue)) || parseInt(chunkOverlapValue) < 0) {
            alert('Please enter a valid chunk overlap (non-negative number).');
            return;
        }

        if (settingsLoading) {
            settingsLoading.style.display = 'block';
        }

        fetch('/api/settings/embedding', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                provider: provider,
                model: model,
                chunk_size: parseInt(chunkSizeValue),
                chunk_overlap: parseInt(chunkOverlapValue),
                recursive_chunking: recursiveChunkingValue === 'true'
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (settingsLoading) {
                settingsLoading.style.display = 'none';
            }

            alert('Embedding settings saved successfully.');
        })
        .catch(error => {
            console.error('Error saving embedding settings:', error);

            if (settingsLoading) {
                settingsLoading.style.display = 'none';
            }

            alert(`Error saving embedding settings: ${error.message}`);
        });
    }

    /**
     * Save database settings
     */
    function saveDatabaseSettings() {
        if (!databaseHost || !databasePort) return;

        const host = databaseHost.value;
        const port = databasePort.value;

        if (!host || !port) {
            alert('Please enter a host and port.');
            return;
        }

        if (settingsLoading) {
            settingsLoading.style.display = 'block';
        }

        fetch('/api/settings/database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                host: host,
                port: parseInt(port)
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (settingsLoading) {
                settingsLoading.style.display = 'none';
            }

            alert('Database settings saved successfully.');
        })
        .catch(error => {
            console.error('Error saving database settings:', error);

            if (settingsLoading) {
                settingsLoading.style.display = 'none';
            }

            alert(`Error saving database settings: ${error.message}`);
        });
    }
});
