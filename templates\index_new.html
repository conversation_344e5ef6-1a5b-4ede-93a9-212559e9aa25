<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
            overflow-x: hidden;
        }
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .score-badge {
            float: right;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
        }
        .vis-network {
            outline: none;
        }

        /* Sidebar styles */
        .app-container {
            display: flex;
            width: 100%;
            min-height: calc(100vh - 40px);
        }

        #sidebar {
            width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: all 0.3s;
            height: calc(100vh - 40px);
            position: sticky;
            top: 20px;
            overflow-y: auto;
        }

        #sidebar.collapsed {
            width: 60px;
        }

        #main-content {
            flex: 1;
            transition: all 0.3s;
            padding: 0 15px;
        }

        #main-content.expanded {
            margin-left: 60px;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h4 {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        #sidebar.collapsed .sidebar-header h4 {
            display: none;
        }

        .sidebar-nav {
            padding: 15px 0;
        }

        .sidebar-nav-item {
            padding: 10px 15px;
            display: flex;
            align-items: center;
            color: #495057;
            text-decoration: none;
            cursor: pointer;
        }

        .sidebar-nav-item:hover {
            background-color: #e9ecef;
        }

        .sidebar-nav-item.active {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .sidebar-nav-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        #sidebar.collapsed .sidebar-nav-item span {
            display: none;
        }

        #sidebar-toggle {
            background: none;
            border: none;
            color: #495057;
            cursor: pointer;
        }

        /* Settings panel styles */
        .settings-panel {
            display: none;
            padding: 15px;
        }

        .settings-section {
            margin-bottom: 30px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        .user-message {
            background-color: #e9ecef;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .assistant-message {
            background-color: #f0f7ff;
            margin-right: auto;
            border-top-left-radius: 0;
            line-height: 1.5;
        }
        .message-container {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-header {
            text-align: right;
            color: #495057;
        }
        .assistant-header {
            color: #0d6efd;
        }
        /* Formatting for message content */
        .message h3 {
            font-size: 1.2rem;
            margin-top: 15px;
            margin-bottom: 10px;
            color: #0d6efd;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        .message h4 {
            font-size: 1.1rem;
            margin-top: 12px;
            margin-bottom: 8px;
            color: #0d6efd;
        }
        .message h5 {
            font-size: 1rem;
            margin-top: 10px;
            margin-bottom: 6px;
            font-weight: bold;
        }
        .message code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            color: #d63384;
        }
        .message pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .message .badge {
            font-weight: normal;
            padding: 4px 6px;
            margin: 0 2px;
        }
        .reference-link {
            color: #0d6efd;
            text-decoration: none;
            font-weight: bold;
        }
        .reference-link:hover {
            text-decoration: underline;
        }
        .highlight-source {
            border: 2px solid #0d6efd;
            box-shadow: 0 0 10px rgba(13, 110, 253, 0.3);
            animation: highlight-pulse 2s ease-in-out;
        }
        @keyframes highlight-pulse {
            0% { box-shadow: 0 0 10px rgba(13, 110, 253, 0.3); }
            50% { box-shadow: 0 0 20px rgba(13, 110, 253, 0.6); }
            100% { box-shadow: 0 0 10px rgba(13, 110, 253, 0.3); }
        }
        /* Upload tab styles */
        #file-dropzone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            margin-bottom: 20px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }
        #file-dropzone.highlight {
            border-color: #007bff;
            background-color: #e8f4ff;
            transform: scale(1.02);
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.2);
        }
        .file-item {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        .upload-section {
            margin-bottom: 30px;
        }
        .upload-options {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .upload-info {
            background-color: #e8f4ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .upload-info h5 {
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div id="sidebar">
            <div class="sidebar-header">
                <h4>Graphiti</h4>
                <button id="sidebar-toggle">
                    <i class="bi bi-chevron-left"></i>
                </button>
            </div>
            <div class="sidebar-nav">
                <div class="sidebar-nav-item" data-target="llm-settings-panel">
                    <i class="bi bi-cpu"></i>
                    <span>LLM Settings</span>
                </div>
                <div class="sidebar-nav-item" data-target="embedding-settings-panel">
                    <i class="bi bi-diagram-3"></i>
                    <span>Embedding Settings</span>
                </div>
                <div class="sidebar-nav-item" data-target="database-settings-panel">
                    <i class="bi bi-database"></i>
                    <span>Database Settings</span>
                </div>
                <div class="sidebar-nav-item" data-target="system-settings-panel">
                    <i class="bi bi-gear"></i>
                    <span>System Settings</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <div class="container">
                <h1 class="mb-4">Knowledge Graph Explorer</h1>

                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="true">Search</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="answer-tab" data-bs-toggle="tab" data-bs-target="#answer" type="button" role="tab" aria-controls="answer" aria-selected="false">Answer Questions</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="false">Upload</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab" aria-controls="documents" aria-selected="false">Documents</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="entities-tab" data-bs-toggle="tab" data-bs-target="#entities" type="button" role="tab" aria-controls="entities" aria-selected="false">Entities</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="graph-tab" data-bs-toggle="tab" data-bs-target="#graph" type="button" role="tab" aria-controls="graph" aria-selected="false">Knowledge Graph</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="references-tab" data-bs-toggle="tab" data-bs-target="#references" type="button" role="tab" aria-controls="references" aria-selected="false">References</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">Settings</button>
                    </li>
                </ul>

        <div class="tab-content" id="myTabContent">
            <!-- Search Tab -->
            <div class="tab-pane fade show active" id="search" role="tabpanel" aria-labelledby="search-tab">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" id="search-input" class="form-control" placeholder="Enter search query...">
                            <button class="btn btn-primary" type="button" id="search-button">Search</button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="search-type">
                            <option value="semantic">Semantic Search</option>
                            <option value="hybrid">Hybrid Search</option>
                        </select>
                    </div>
                </div>

                <div id="search-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Searching...</p>
                </div>

                <div id="search-results"></div>
            </div>

            <!-- Answer Tab -->
            <div class="tab-pane fade" id="answer" role="tabpanel" aria-labelledby="answer-tab">
                <div class="row mb-4">
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="text" id="question-input" class="form-control" placeholder="Ask a question...">
                            <button class="btn btn-primary" type="button" id="question-button">Ask</button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" type="button" id="clear-chat-button">Clear Chat</button>
                    </div>
                </div>

                <div id="conversation-container" class="mb-4" style="max-height: 500px; overflow-y: auto;">
                    <div id="conversation-history"></div>
                </div>

                <div id="answer-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Generating answer...</p>
                </div>

                <div id="answer-result" style="display: none;">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title" id="question-display"></h5>
                        </div>
                        <div class="card-body">
                            <div id="answer-text"></div>
                        </div>
                    </div>
                </div>

                <div id="answer-sources" style="display: none;">
                    <h5>Sources:</h5>
                    <div id="sources-list"></div>
                </div>
            </div>

            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                <div class="row">
                    <div class="col-md-8">
                        <div class="upload-section">
                            <h4>Upload Documents</h4>
                            <p class="text-muted">Add documents to your knowledge graph for processing and analysis.</p>

                            <div id="upload-alerts"></div>

                            <div id="file-dropzone">
                                <h5>Drag & Drop Files Here</h5>
                                <p>or</p>
                                <input type="file" id="file-input" class="d-none" multiple accept=".pdf,.jpg,.jpeg,.png,.tiff,.bmp,.txt,.doc,.docx">
                                <button class="btn btn-outline-primary" onclick="document.getElementById('file-input').click()">Browse Files</button>
                                <p class="mt-2 text-muted small">Supported formats: PDF, Images (JPG, PNG, TIFF, BMP), Text files (TXT), Word documents (DOC, DOCX)</p>
                            </div>

                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Selected Files</h5>
                                </div>
                                <div class="card-body">
                                    <div id="file-list">
                                        <p class="text-muted">No files selected</p>
                                    </div>
                                    <button id="upload-button" class="btn btn-primary mt-3" style="display: none;">Process Files</button>
                                </div>
                            </div>

                            <div id="upload-progress" style="display: none;">
                                <h5>Processing Files...</h5>
                                <div class="progress mb-3">
                                    <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p id="upload-status">Preparing...</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="upload-options">
                            <h5>Processing Options</h5>
                            <div class="mb-3">
                                <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                                <input type="number" class="form-control" id="chunk-size" value="1200" min="100" max="10000">
                                <div class="form-text">Recommended: 1200 characters</div>
                            </div>
                            <div class="mb-3">
                                <label for="overlap-size" class="form-label">Overlap Size (characters)</label>
                                <input type="number" class="form-control" id="overlap-size" value="0" min="0" max="500">
                                <div class="form-text">Recommended: 0 characters</div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="extract-entities" checked>
                                <label class="form-check-label" for="extract-entities">Extract entities automatically</label>
                                <div class="form-text">Automatically run entity extraction after processing the document</div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="extract-references" checked>
                                <label class="form-check-label" for="extract-references">Extract references automatically</label>
                                <div class="form-text">Automatically extract references and citations from the document</div>
                            </div>
                        </div>

                        <div class="upload-info">
                            <h5>About Document Processing</h5>
                            <p>When you upload PDF documents, they are:</p>
                            <ol>
                                <li><strong>Chunked</strong> into smaller pieces for better processing</li>
                                <li><strong>Embedded</strong> using vector representations</li>
                                <li><strong>Analyzed</strong> for entities and relationships</li>
                                <li><strong>Stored</strong> in the knowledge graph</li>
                            </ol>
                            <p><strong>Chunking Strategy:</strong></p>
                            <ul>
                                <li>Documents are split into chunks of approximately 1200 characters</li>
                                <li>Recursive chunking is used to preserve natural boundaries (paragraphs, sentences)</li>
                                <li>No overlap between chunks to avoid duplication</li>
                            </ul>
                            <p><strong>Storage in FalkorDB:</strong></p>
                            <ul>
                                <li>Each document becomes an Episode node</li>
                                <li>Each chunk becomes a Fact node</li>
                                <li>Entities are extracted and linked to Facts</li>
                                <li>All data is stored in the FalkorDB graph database</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Tab -->
            <div class="tab-pane fade" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                <div id="documents-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading documents...</p>
                </div>

                <div id="documents-list"></div>
            </div>

            <!-- Entities Tab -->
            <div class="tab-pane fade" id="entities" role="tabpanel" aria-labelledby="entities-tab">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" id="entity-search-input" class="form-control" placeholder="Search entities...">
                            <button class="btn btn-primary" type="button" id="entity-search-button">Search</button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="entity-type-filter">
                            <option value="">All Entity Types</option>
                            <option value="Person">Person</option>
                            <option value="Organization">Organization</option>
                            <option value="Location">Location</option>
                            <option value="Treatment">Treatment</option>
                            <option value="Herb">Herb</option>
                            <option value="Medication">Medication</option>
                            <option value="Disease">Disease</option>
                            <option value="Symptom">Symptom</option>
                            <option value="Nutrient">Nutrient</option>
                            <option value="Concept">Concept</option>
                            <option value="Process">Process</option>
                            <option value="Research">Research</option>
                            <option value="Study">Study</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="entity-sort-filter">
                            <option value="name">Sort by Name</option>
                            <option value="mentions-desc">Sort by Mentions (High to Low)</option>
                            <option value="mentions-asc">Sort by Mentions (Low to High)</option>
                        </select>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Mention Count Filter</h5>
                            </div>
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <label for="mention-count-slider" class="form-label">Minimum Mentions: <span id="mention-count-value">0</span></label>
                                        <input type="range" class="form-range" id="mention-count-slider" min="0" max="50" value="0">
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary w-100" id="apply-mention-filter">Apply Filter</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Entity Frequency</h5>
                            </div>
                            <div class="card-body">
                                <div style="height: 120px; position: relative;">
                                    <canvas id="entity-frequency-chart"></canvas>
                                    <div class="text-center text-muted" id="chart-placeholder" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                        <p>Select an entity type to view frequency distribution</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="entities-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading entities...</p>
                </div>

                <div id="entity-details" class="card mb-4" style="display: none;">
                    <div class="card-header">
                        <h5 class="card-title" id="entity-name"></h5>
                        <span class="badge bg-info" id="entity-type"></span>
                    </div>
                    <div class="card-body">
                        <p id="entity-description"></p>
                        <h6>Mentioned in:</h6>
                        <div id="entity-mentions"></div>
                        <h6>Related to:</h6>
                        <div id="entity-relationships"></div>
                    </div>
                </div>

                <div id="entities-list"></div>
            </div>

            <!-- Graph Visualization Tab -->
            <div class="tab-pane fade" id="graph" role="tabpanel" aria-labelledby="graph-tab">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4>Knowledge Graph Visualization</h4>
                            <button id="refresh-graph" class="btn btn-outline-primary">Refresh Graph</button>
                        </div>
                    </div>
                </div>

                <div id="graph-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading knowledge graph...</p>
                </div>

                <div id="graph-container" style="height: 600px; border: 1px solid #ddd; border-radius: 4px;"></div>
            </div>

            <!-- References Tab -->
            <div class="tab-pane fade" id="references" role="tabpanel" aria-labelledby="references-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Document References</h5>
                    </div>
                    <div class="card-body">
                        <div id="references-alert-container">
                            <!-- Alerts will be displayed here -->
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="document-select" class="form-label">Select Document</label>
                                <select class="form-select" id="document-select">
                                    <option value="">Select a document</option>
                                    <!-- Documents will be populated dynamically -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-end h-100">
                                    <div>
                                        <label class="form-label">References</label>
                                        <div><span id="references-count">0</span> references found</div>
                                    </div>
                                    <div>
                                        <button class="btn btn-outline-primary btn-sm" id="download-csv-button" disabled>
                                            <i class="bi bi-download"></i> Download CSV
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" id="download-all-csv-button">
                                            <i class="bi bi-download"></i> Download All CSV
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="reference-search-input" class="form-label">Search References</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="reference-search-input" placeholder="Search in references...">
                                    <button class="btn btn-primary" type="button" id="reference-search-button">Search</button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="reference-filter-select" class="form-label">Filter By</label>
                                <select class="form-select" id="reference-filter-select">
                                    <option value="all">All References</option>
                                    <option value="llm">LLM Extracted</option>
                                    <option value="regex">Regex Extracted</option>
                                </select>
                            </div>
                        </div>

                        <div id="references-content">
                            <div id="references-list">
                                <!-- References will be displayed here -->
                                <div class="alert alert-info">Select a document to view its references.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h4>Settings</h4>
                        <p class="text-muted">Configure your Graphiti knowledge graph settings.</p>
                    </div>
                </div>

                <!-- Settings Panels -->
                <div class="settings-panels">
                    <!-- LLM Settings Panel -->
                    <div id="llm-settings-panel" class="settings-panel">
                        <h5 class="mb-3">LLM Settings</h5>
                        <p class="text-muted">Configure the Large Language Model used for question answering.</p>

                        <div id="llm-settings-loading" class="loading" style="display: block;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading LLM settings...</p>
                        </div>

                        <div id="llm-settings-saving" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Saving LLM settings...</p>
                        </div>

                        <div id="llm-settings-error" class="alert alert-danger" style="display: none;"></div>

                        <div id="llm-settings-alerts"></div>

                        <div id="llm-settings-content" style="display: none;">
                            <form id="llm-settings-form">
                                <div class="mb-3">
                                    <label for="llm-provider" class="form-label">LLM Provider</label>
                                    <select class="form-select" id="llm-provider" required>
                                        <option value="openrouter">Open Router</option>
                                        <option value="openai">OpenAI</option>
                                        <option value="local">Local (Ollama)</option>
                                        <option value="gemini">Google Gemini</option>
                                    </select>
                                    <div class="form-text">Select the provider for your Large Language Model.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="llm-model" class="form-label">LLM Model</label>
                                    <select class="form-select" id="llm-model" required>
                                        <!-- Options will be populated dynamically -->
                                    </select>
                                    <div class="form-text">Select the model to use for question answering.</div>
                                </div>

                                <button type="submit" class="btn btn-primary">Save LLM Settings</button>
                            </form>
                        </div>
                    </div>

                    <!-- Embedding Settings Panel -->
                    <div id="embedding-settings-panel" class="settings-panel">
                        <h5 class="mb-3">Embedding Settings</h5>
                        <p class="text-muted">Configure the embedding model used for semantic search.</p>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Embedding settings configuration will be available in a future update.
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Current Embedding Configuration</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Chunking Method:</span>
                                        <span class="badge bg-primary">Recursive</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Chunk Size:</span>
                                        <span class="badge bg-primary">1200 characters</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Overlap:</span>
                                        <span class="badge bg-primary">0 characters</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Database Settings Panel -->
                    <div id="database-settings-panel" class="settings-panel">
                        <h5 class="mb-3">Database Settings</h5>
                        <p class="text-muted">Configure your FalkorDB database connection.</p>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> FalkorDB settings configuration will be available in a future update.
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Current FalkorDB Configuration</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Host:</span>
                                        <span class="badge bg-primary">localhost</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Port:</span>
                                        <span class="badge bg-primary">6379</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Graph Name:</span>
                                        <span class="badge bg-primary">graphiti</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings Panel -->
                    <div id="system-settings-panel" class="settings-panel">
                        <h5 class="mb-3">System Settings</h5>
                        <p class="text-muted">Configure general system settings.</p>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> System settings configuration will be available in a future update.
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/add_enhancements_link.js"></script>
    <script src="/static/graph_visualization.js"></script>
    <script src="/static/conversation.js"></script>
    <script src="/static/upload.js"></script>
    <script src="/static/documents.js"></script>
    <script src="/static/entities.js"></script>
    <script src="/static/references.js"></script>
    <script src="/static/settings.js"></script>
    <!-- Removed test_entities.js -->
</body>
</html>
