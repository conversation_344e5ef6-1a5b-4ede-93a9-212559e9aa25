"""
Comprehensive script to ensure all documents, entities, nodes, and relationships have correct UUIDs.

This script:
1. Checks for missing UUIDs across all node and relationship types
2. Fixes any missing UUIDs
3. Validates UUID consistency in merged entities
4. Fixes any inconsistencies found
5. Provides a detailed report of all issues and fixes

Usage:
    python scripts/ensure_all_uuids.py [--fix] [--verbose]
"""

import os
import asyncio
import logging
import uuid
import argparse
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple, Set

import sys
from pathlib import Path

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from database.database_service import get_falkordb_adapter
from utils.uuid_validation import (
    is_valid_uuid,
    generate_uuid,
    find_duplicate_uuids,
    validate_uuid_references
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UUIDValidator:
    """Class to validate and fix UUIDs in the database."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        """
        Initialize the validator.

        Args:
            fix: Whether to fix issues
            verbose: Whether to enable verbose logging
        """
        self.fix = fix
        self.verbose = verbose
        self.adapter = None

        # Statistics
        self.stats = {
            "episodes_total": 0,
            "episodes_missing_uuid": 0,
            "episodes_fixed": 0,
            "facts_total": 0,
            "facts_missing_uuid": 0,
            "facts_fixed": 0,
            "entities_total": 0,
            "entities_missing_uuid": 0,
            "entities_fixed": 0,
            "relationships_total": 0,
            "relationships_missing_uuid": 0,
            "relationships_fixed": 0,
            "orphaned_relationships": 0,
            "orphaned_relationships_fixed": 0,
            "inconsistent_relationships": 0,
            "inconsistent_relationships_fixed": 0,
            "duplicate_uuids": 0,
            "duplicate_uuids_fixed": 0
        }

    async def get_adapter(self):
        """Get the database adapter."""
        if not self.adapter:
            self.adapter = await get_falkordb_adapter()
        return self.adapter

    async def check_episodes(self):
        """Check Episode nodes for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all Episode nodes
        count_query = """
        MATCH (e:Episode)
        RETURN count(e) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["episodes_total"] = count_result[1][0][0]

        # Find Episode nodes without UUIDs
        missing_query = """
        MATCH (e:Episode)
        WHERE e.uuid IS NULL
        RETURN id(e) as id, e.name as name
        """

        missing_result = adapter.execute_cypher(missing_query)
        episodes = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                episodes.append({
                    "id": row[0],
                    "name": row[1]
                })

            self.stats["episodes_missing_uuid"] = len(episodes)

            if self.verbose:
                logger.info(f"Found {len(episodes)} Episode nodes without UUIDs")

            if self.fix:
                fixed_count = await self.fix_episodes(episodes)
                self.stats["episodes_fixed"] = fixed_count

    async def fix_episodes(self, episodes: List[Dict[str, Any]]) -> int:
        """
        Fix Episode nodes missing UUIDs.

        Args:
            episodes: List of Episode nodes to fix

        Returns:
            Number of nodes fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for episode in episodes:
            episode_uuid = generate_uuid()
            timestamp = datetime.now(timezone.utc).isoformat()

            update_query = f"""
            MATCH (e:Episode)
            WHERE id(e) = {episode['id']}
            SET e.uuid = '{episode_uuid}',
                e.updated_at = '{timestamp}'
            RETURN e.name as name, e.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                if self.verbose:
                    logger.info(f"Fixed Episode node: {update_result[1][0][0]} with UUID {update_result[1][0][1]}")

        return fixed_count

    async def check_facts(self):
        """Check Fact nodes for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all Fact nodes
        count_query = """
        MATCH (f:Fact)
        RETURN count(f) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["facts_total"] = count_result[1][0][0]

        # Find Fact nodes without UUIDs
        missing_query = """
        MATCH (f:Fact)
        WHERE f.uuid IS NULL
        RETURN id(f) as id
        LIMIT 1000
        """

        missing_result = adapter.execute_cypher(missing_query)
        facts = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                facts.append({
                    "id": row[0]
                })

            self.stats["facts_missing_uuid"] = len(facts)

            if self.verbose:
                logger.info(f"Found {len(facts)} Fact nodes without UUIDs")

            if self.fix:
                fixed_count = await self.fix_facts(facts)
                self.stats["facts_fixed"] = fixed_count

    async def fix_facts(self, facts: List[Dict[str, Any]]) -> int:
        """
        Fix Fact nodes missing UUIDs.

        Args:
            facts: List of Fact nodes to fix

        Returns:
            Number of nodes fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for fact in facts:
            fact_uuid = generate_uuid()
            timestamp = datetime.now(timezone.utc).isoformat()

            update_query = f"""
            MATCH (f:Fact)
            WHERE id(f) = {fact['id']}
            SET f.uuid = '{fact_uuid}',
                f.updated_at = '{timestamp}'
            RETURN f.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                if self.verbose:
                    logger.info(f"Fixed Fact node with UUID {update_result[1][0][0]}")

        return fixed_count

    async def check_entities(self):
        """Check Entity nodes for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all Entity nodes
        count_query = """
        MATCH (e:Entity)
        RETURN count(e) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["entities_total"] = count_result[1][0][0]

        # Find Entity nodes without UUIDs
        missing_query = """
        MATCH (e:Entity)
        WHERE e.uuid IS NULL
        RETURN id(e) as id, e.name as name, e.type as type
        """

        missing_result = adapter.execute_cypher(missing_query)
        entities = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                entities.append({
                    "id": row[0],
                    "name": row[1],
                    "type": row[2]
                })

            self.stats["entities_missing_uuid"] = len(entities)

            if self.verbose:
                logger.info(f"Found {len(entities)} Entity nodes without UUIDs")

            if self.fix:
                fixed_count = await self.fix_entities(entities)
                self.stats["entities_fixed"] = fixed_count

    async def fix_entities(self, entities: List[Dict[str, Any]]) -> int:
        """
        Fix Entity nodes missing UUIDs.

        Args:
            entities: List of Entity nodes to fix

        Returns:
            Number of nodes fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for entity in entities:
            entity_uuid = generate_uuid()
            timestamp = datetime.now(timezone.utc).isoformat()

            update_query = f"""
            MATCH (e:Entity)
            WHERE id(e) = {entity['id']}
            SET e.uuid = '{entity_uuid}',
                e.updated_at = '{timestamp}'
            RETURN e.name as name, e.type as type, e.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                if self.verbose:
                    logger.info(f"Fixed Entity node: {update_result[1][0][0]} ({update_result[1][0][1]}) with UUID {update_result[1][0][2]}")

        return fixed_count

    async def check_relationships(self):
        """Check relationships for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all relationships
        count_query = """
        MATCH ()-[r]->()
        RETURN count(r) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["relationships_total"] = count_result[1][0][0]

        # Find relationships without UUIDs
        missing_query = """
        MATCH ()-[r]->()
        WHERE r.uuid IS NULL
        RETURN id(r) as id, type(r) as type
        LIMIT 10000
        """

        missing_result = adapter.execute_cypher(missing_query)
        relationships = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                relationships.append({
                    "id": row[0],
                    "type": row[1]
                })

            self.stats["relationships_missing_uuid"] = len(relationships)

            if self.verbose:
                logger.info(f"Found {len(relationships)} relationships without UUIDs")

            if self.fix:
                fixed_count = await self.fix_relationships(relationships)
                self.stats["relationships_fixed"] = fixed_count

    async def fix_relationships(self, relationships: List[Dict[str, Any]]) -> int:
        """
        Fix relationships missing UUIDs.

        Args:
            relationships: List of relationships to fix

        Returns:
            Number of relationships fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for rel in relationships:
            rel_uuid = generate_uuid()
            timestamp = datetime.now(timezone.utc).isoformat()

            update_query = f"""
            MATCH ()-[r]->()
            WHERE id(r) = {rel['id']}
            SET r.uuid = '{rel_uuid}',
                r.updated_at = '{timestamp}'
            RETURN type(r) as type, r.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                if self.verbose:
                    logger.info(f"Fixed relationship of type {update_result[1][0][0]} with UUID {update_result[1][0][1]}")

        return fixed_count

    async def check_merged_entities(self):
        """Check for issues with merged entities."""
        await self.check_orphaned_relationships()
        await self.check_relationship_consistency()

    async def check_orphaned_relationships(self):
        """Check for orphaned relationships after entity merges."""
        adapter = await self.get_adapter()

        # Find relationships pointing to non-existent entities
        query = """
        MATCH ()-[r]->(e:Entity)
        WHERE NOT EXISTS(e.uuid)
        RETURN id(r) as rel_id, type(r) as rel_type
        LIMIT 100
        """

        result = adapter.execute_cypher(query)
        orphaned_relationships = []

        if result and len(result) > 1 and len(result[1]) > 0:
            for row in result[1]:
                orphaned_relationships.append({
                    "rel_id": row[0],
                    "rel_type": row[1]
                })

            self.stats["orphaned_relationships"] = len(orphaned_relationships)

            if self.verbose:
                logger.info(f"Found {len(orphaned_relationships)} orphaned relationships")

            if self.fix:
                fixed_count = await self.fix_orphaned_relationships(orphaned_relationships)
                self.stats["orphaned_relationships_fixed"] = fixed_count

    async def fix_orphaned_relationships(self, orphaned_relationships: List[Dict[str, Any]]) -> int:
        """
        Fix orphaned relationships by removing them.

        Args:
            orphaned_relationships: List of orphaned relationships

        Returns:
            Number of relationships fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for rel in orphaned_relationships:
            # Delete the orphaned relationship
            query = f"""
            MATCH ()-[r]->()
            WHERE id(r) = {rel['rel_id']}
            DELETE r
            """

            result = adapter.execute_cypher(query)

            if result:
                fixed_count += 1
                if self.verbose:
                    logger.info(f"Deleted orphaned relationship of type {rel['rel_type']}")

        return fixed_count

    async def check_relationship_consistency(self):
        """Check for inconsistent relationships after entity merges."""
        adapter = await self.get_adapter()

        # Find relationships with inconsistent UUIDs
        query = """
        MATCH (source)-[r]->(target:Entity)
        WHERE EXISTS(r.target_uuid) AND r.target_uuid <> target.uuid
        RETURN id(r) as rel_id, type(r) as rel_type,
               source.uuid as source_uuid, target.uuid as target_uuid,
               r.target_uuid as stored_target_uuid
        LIMIT 100
        """

        result = adapter.execute_cypher(query)
        inconsistent_relationships = []

        if result and len(result) > 1 and len(result[1]) > 0:
            for row in result[1]:
                inconsistent_relationships.append({
                    "rel_id": row[0],
                    "rel_type": row[1],
                    "source_uuid": row[2],
                    "target_uuid": row[3],
                    "stored_target_uuid": row[4]
                })

            self.stats["inconsistent_relationships"] = len(inconsistent_relationships)

            if self.verbose:
                logger.info(f"Found {len(inconsistent_relationships)} inconsistent relationships")

            if self.fix:
                fixed_count = await self.fix_relationship_consistency(inconsistent_relationships)
                self.stats["inconsistent_relationships_fixed"] = fixed_count

    async def fix_relationship_consistency(self, inconsistent_relationships: List[Dict[str, Any]]) -> int:
        """
        Fix inconsistent relationships by updating the stored UUID.

        Args:
            inconsistent_relationships: List of inconsistent relationships

        Returns:
            Number of relationships fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for rel in inconsistent_relationships:
            # Update the relationship to use the correct target UUID
            query = f"""
            MATCH ()-[r]->()
            WHERE id(r) = {rel['rel_id']}
            SET r.target_uuid = '{rel['target_uuid']}',
                r.updated_at = '{datetime.now(timezone.utc).isoformat()}'
            """

            result = adapter.execute_cypher(query)

            if result:
                fixed_count += 1
                if self.verbose:
                    logger.info(f"Fixed inconsistent relationship of type {rel['rel_type']}")

        return fixed_count

    async def check_duplicate_uuids(self):
        """Check for duplicate UUIDs."""
        adapter = await self.get_adapter()

        # Check for duplicate UUIDs in Entity nodes
        query = """
        MATCH (e:Entity)
        WITH e.uuid as uuid, collect(id(e)) as ids
        WHERE size(ids) > 1
        RETURN uuid, size(ids) as count
        LIMIT 100
        """

        result = adapter.execute_cypher(query)
        duplicate_uuids = []

        if result and len(result) > 1 and len(result[1]) > 0:
            for row in result[1]:
                duplicate_uuids.append({
                    "uuid": row[0],
                    "count": row[1]
                })

            self.stats["duplicate_uuids"] = sum(d["count"] - 1 for d in duplicate_uuids)

            if self.verbose:
                logger.info(f"Found {len(duplicate_uuids)} UUIDs with duplicates, affecting {self.stats['duplicate_uuids']} nodes")

            if self.fix:
                fixed_count = await self.fix_duplicate_uuids(duplicate_uuids)
                self.stats["duplicate_uuids_fixed"] = fixed_count

    async def fix_duplicate_uuids(self, duplicate_uuids: List[Dict[str, Any]]) -> int:
        """
        Fix duplicate UUIDs by assigning new UUIDs to duplicates.

        Args:
            duplicate_uuids: List of duplicate UUIDs

        Returns:
            Number of duplicates fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for dup in duplicate_uuids:
            # Get all nodes with this UUID
            query = f"""
            MATCH (e:Entity)
            WHERE e.uuid = '{dup['uuid']}'
            RETURN id(e) as id, e.name as name, e.type as type
            """

            result = adapter.execute_cypher(query)
            nodes = []

            if result and len(result) > 1 and len(result[1]) > 0:
                for row in result[1]:
                    nodes.append({
                        "id": row[0],
                        "name": row[1],
                        "type": row[2]
                    })

                # Keep the first node, update the rest
                for node in nodes[1:]:
                    new_uuid = generate_uuid()
                    timestamp = datetime.now(timezone.utc).isoformat()

                    update_query = f"""
                    MATCH (e:Entity)
                    WHERE id(e) = {node['id']}
                    SET e.uuid = '{new_uuid}',
                        e.updated_at = '{timestamp}'
                    RETURN e.name as name, e.type as type, e.uuid as uuid
                    """

                    update_result = adapter.execute_cypher(update_query)

                    if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                        fixed_count += 1
                        if self.verbose:
                            logger.info(f"Fixed duplicate UUID for entity: {update_result[1][0][0]} ({update_result[1][0][1]}) with new UUID {update_result[1][0][2]}")

        return fixed_count

    async def run_all_checks(self):
        """Run all checks and fixes."""
        start_time = time.time()

        logger.info("Starting UUID validation...")

        # Check for missing UUIDs
        await self.check_episodes()
        await self.check_facts()
        await self.check_entities()
        await self.check_relationships()

        # Check for issues with merged entities
        await self.check_merged_entities()

        # Check for duplicate UUIDs
        await self.check_duplicate_uuids()

        # Print summary
        logger.info("\nUUID Validation Summary:")
        logger.info(f"Episodes: {self.stats['episodes_missing_uuid']} out of {self.stats['episodes_total']} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['episodes_fixed']}")

        logger.info(f"Facts: {self.stats['facts_missing_uuid']} out of {self.stats['facts_total']} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['facts_fixed']}")

        logger.info(f"Entities: {self.stats['entities_missing_uuid']} out of {self.stats['entities_total']} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['entities_fixed']}")

        logger.info(f"Relationships: {self.stats['relationships_missing_uuid']} out of {self.stats['relationships_total']} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['relationships_fixed']}")

        logger.info(f"Orphaned relationships: {self.stats['orphaned_relationships']}")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['orphaned_relationships_fixed']}")

        logger.info(f"Inconsistent relationships: {self.stats['inconsistent_relationships']}")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['inconsistent_relationships_fixed']}")

        logger.info(f"Duplicate UUIDs: {self.stats['duplicate_uuids']}")
        if self.fix:
            logger.info(f"  - Fixed: {self.stats['duplicate_uuids_fixed']}")

        # Total issues
        total_issues = (
            self.stats["episodes_missing_uuid"] +
            self.stats["facts_missing_uuid"] +
            self.stats["entities_missing_uuid"] +
            self.stats["relationships_missing_uuid"] +
            self.stats["orphaned_relationships"] +
            self.stats["inconsistent_relationships"] +
            self.stats["duplicate_uuids"]
        )

        # Total fixed
        total_fixed = (
            self.stats["episodes_fixed"] +
            self.stats["facts_fixed"] +
            self.stats["entities_fixed"] +
            self.stats["relationships_fixed"] +
            self.stats["orphaned_relationships_fixed"] +
            self.stats["inconsistent_relationships_fixed"] +
            self.stats["duplicate_uuids_fixed"]
        )

        logger.info(f"\nTotal issues: {total_issues}")
        if self.fix:
            logger.info(f"Total fixed: {total_fixed}")

        end_time = time.time()
        logger.info(f"\nValidation completed in {end_time - start_time:.2f} seconds")

async def main():
    """Main function."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Ensure all elements have correct UUIDs")
    parser.add_argument("--fix", action="store_true", help="Fix any issues found")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    try:
        # Create validator
        validator = UUIDValidator(fix=args.fix, verbose=args.verbose)

        # Run all checks
        await validator.run_all_checks()

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
