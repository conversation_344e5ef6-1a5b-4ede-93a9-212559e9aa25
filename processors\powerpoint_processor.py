"""
Microsoft PowerPoint Presentation Processor
Handles .ppt and .pptx files with enhanced text extraction.
"""

import os
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class PowerPointProcessor:
    """
    Processor for PowerPoint presentations (.ppt, .pptx).
    """
    
    def __init__(self):
        """Initialize the PowerPoint processor."""
        self.supported_extensions = ['.ppt', '.pptx']
        
        # Try to import required libraries
        self.pptx_available = self._check_pptx_availability()
        self.mistral_ocr = self._initialize_mistral_ocr()
    
    def _check_pptx_availability(self) -> bool:
        """Check if python-pptx is available."""
        try:
            from pptx import Presentation
            return True
        except ImportError:
            logger.warning("python-pptx not available. Install with: pip install python-pptx")
            return False
    
    def _initialize_mistral_ocr(self):
        """Initialize Mistral OCR if available."""
        try:
            from utils.mistral_ocr import MistralOCRProcessor
            return MistralOCRProcessor()
        except Exception as e:
            logger.warning(f"Mistral OCR not available: {e}")
            return None
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text and metadata from a PowerPoint presentation.
        
        Args:
            file_path: Path to the PowerPoint file
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_extensions:
                return {
                    'success': False,
                    'error': f"Unsupported file extension: {file_ext}",
                    'text': '',
                    'metadata': {}
                }
            
            # Try Mistral OCR first (supports both .ppt and .pptx)
            if self.mistral_ocr:
                try:
                    logger.info(f"Extracting text from {file_path} using Mistral OCR")
                    text = await self.mistral_ocr.extract_text_from_document(str(file_path))
                    
                    if text and len(text.strip()) > 0:
                        metadata = await self._extract_metadata_mistral(file_path)
                        return {
                            'success': True,
                            'text': text.strip(),
                            'metadata': metadata,
                            'ocr_provider': 'mistral-ocr',
                            'extraction_method': 'mistral_ocr'
                        }
                    else:
                        logger.warning(f"Mistral OCR returned empty text for {file_path}")
                except Exception as e:
                    logger.warning(f"Mistral OCR failed for {file_path}: {e}")
            
            # Fallback to python-pptx for .pptx files
            if file_ext == '.pptx' and self.pptx_available:
                try:
                    logger.info(f"Extracting text from {file_path} using python-pptx")
                    result = await self._extract_with_pptx(file_path)
                    
                    if result['success']:
                        return result
                except Exception as e:
                    logger.warning(f"python-pptx extraction failed for {file_path}: {e}")
            
            # If all methods fail
            return {
                'success': False,
                'error': f"Failed to extract text from {file_path}. No suitable extraction method available.",
                'text': '',
                'metadata': {}
            }
            
        except Exception as e:
            logger.error(f"Error processing PowerPoint presentation {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_pptx(self, file_path: Path) -> Dict[str, Any]:
        """Extract text using python-pptx library."""
        try:
            from pptx import Presentation
            
            prs = Presentation(file_path)
            
            slides_text = []
            total_shapes = 0
            total_text_shapes = 0
            
            # Extract text from each slide
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_content = []
                slide_content.append(f"\n=== Slide {slide_num} ===")
                
                # Extract text from shapes
                slide_text_found = False
                for shape in slide.shapes:
                    total_shapes += 1
                    
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_content.append(shape.text.strip())
                        slide_text_found = True
                        total_text_shapes += 1
                    
                    # Extract text from tables
                    if hasattr(shape, "table"):
                        table_text = self._extract_table_text(shape.table)
                        if table_text:
                            slide_content.append("Table content:")
                            slide_content.append(table_text)
                            slide_text_found = True
                
                # Extract notes if available
                if slide.notes_slide and hasattr(slide.notes_slide, 'notes_text_frame'):
                    notes_text = slide.notes_slide.notes_text_frame.text.strip()
                    if notes_text:
                        slide_content.append(f"Notes: {notes_text}")
                        slide_text_found = True
                
                if slide_text_found:
                    slides_text.extend(slide_content)
                else:
                    slides_text.append(f"\n=== Slide {slide_num} ===")
                    slides_text.append("[No text content found]")
            
            text = '\n'.join(slides_text)
            
            # Extract metadata
            metadata = await self._extract_metadata_pptx(prs, file_path)
            metadata.update({
                'total_slides': len(prs.slides),
                'total_shapes': total_shapes,
                'text_shapes': total_text_shapes,
                'extraction_method': 'python_pptx'
            })
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'python-pptx',
                'extraction_method': 'python_pptx',
                'slides_count': len(prs.slides),
                'shapes_count': total_shapes
            }
            
        except Exception as e:
            logger.error(f"Error extracting with python-pptx: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    def _extract_table_text(self, table) -> str:
        """Extract text from a PowerPoint table."""
        try:
            table_rows = []
            for row in table.rows:
                row_cells = []
                for cell in row.cells:
                    cell_text = cell.text.strip() if cell.text else ''
                    row_cells.append(cell_text)
                if any(cell for cell in row_cells):  # Only add non-empty rows
                    table_rows.append(' | '.join(row_cells))
            
            return '\n'.join(table_rows)
        except Exception as e:
            logger.warning(f"Error extracting table text: {e}")
            return ''
    
    async def _extract_metadata_pptx(self, prs, file_path: Path) -> Dict[str, Any]:
        """Extract metadata from pptx presentation."""
        try:
            core_props = prs.core_properties
            
            metadata = {
                'title': core_props.title or file_path.stem,
                'author': core_props.author or 'Unknown',
                'subject': core_props.subject or '',
                'keywords': core_props.keywords or '',
                'comments': core_props.comments or '',
                'created': core_props.created.isoformat() if core_props.created else '',
                'modified': core_props.modified.isoformat() if core_props.modified else '',
                'last_modified_by': core_props.last_modified_by or '',
                'revision': core_props.revision or '',
                'category': core_props.category or '',
                'language': core_props.language or '',
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
            
            # Add presentation-specific metadata
            try:
                slide_layouts = set()
                for slide in prs.slides:
                    if hasattr(slide, 'slide_layout') and hasattr(slide.slide_layout, 'name'):
                        slide_layouts.add(slide.slide_layout.name)
                
                metadata.update({
                    'slide_layouts': list(slide_layouts),
                    'slide_width': prs.slide_width,
                    'slide_height': prs.slide_height
                })
            except Exception as e:
                logger.warning(f"Error extracting presentation metadata: {e}")
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting metadata: {e}")
            return {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    async def _extract_metadata_mistral(self, file_path: Path) -> Dict[str, Any]:
        """Extract basic metadata when using Mistral OCR."""
        try:
            stat = file_path.stat()
            
            return {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'mistral_ocr'
            }
            
        except Exception as e:
            logger.warning(f"Error extracting basic metadata: {e}")
            return {
                'title': file_path.stem,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the presentation content.
        
        Args:
            file_path: Path to the presentation
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars]
            
            if len(text) > max_chars:
                preview_text += "... [truncated]"
            
            return {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text),
                'metadata': result['metadata'],
                'extraction_method': result.get('extraction_method', 'unknown'),
                'slides_count': result.get('slides_count', 0)
            }
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
    
    async def extract_slide_summaries(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract individual slide summaries for better organization.
        
        Args:
            file_path: Path to the presentation
            
        Returns:
            Dictionary containing slide-by-slide information
        """
        try:
            if not self.pptx_available:
                return {
                    'success': False,
                    'error': 'python-pptx not available for slide extraction',
                    'slides': []
                }
            
            from pptx import Presentation
            
            prs = Presentation(file_path)
            slides_info = []
            
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_text = []
                
                # Extract text from shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())
                
                # Extract notes
                notes = ''
                if slide.notes_slide and hasattr(slide.notes_slide, 'notes_text_frame'):
                    notes = slide.notes_slide.notes_text_frame.text.strip()
                
                slides_info.append({
                    'slide_number': slide_num,
                    'text': '\n'.join(slide_text),
                    'notes': notes,
                    'shapes_count': len(slide.shapes)
                })
            
            return {
                'success': True,
                'slides': slides_info,
                'total_slides': len(slides_info)
            }
            
        except Exception as e:
            logger.error(f"Error extracting slide summaries: {e}")
            return {
                'success': False,
                'error': str(e),
                'slides': []
            }
