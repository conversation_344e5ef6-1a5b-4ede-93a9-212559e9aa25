"""
Knowledge graph routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import List, Dict, Any, Optional

from services.knowledge_graph_service import (
    get_knowledge_graph,
    execute_graph_query,
    get_taxonomy,
    create_entity_relationship
)
from database.database_service import get_falkordb_adapter
from models.knowledge_graph import GraphData, GraphQuery, Taxonomy
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["knowledge-graph"])

@router.get("/knowledge-graph", response_model=GraphData)
async def get_graph(limit: int = Query(100, ge=1, le=1000)):
    """
    Get knowledge graph data.

    Args:
        limit: Maximum number of nodes to return

    Returns:
        Graph data
    """
    try:
        graph_data = await get_knowledge_graph(limit)
        return graph_data

    except Exception as e:
        logger.error(f"Error getting knowledge graph: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting knowledge graph: {str(e)}"
        )

@router.get("/knowledge-graph/graph", response_model=GraphData)
async def get_graph_visualization(
    limit: int = Query(100, ge=1, le=1000),
    entity_type: Optional[str] = Query(None),
    relationship_type: Optional[str] = Query(None)
):
    """
    Get knowledge graph data for visualization.

    Args:
        limit: Maximum number of nodes to return
        entity_type: Filter by entity type
        relationship_type: Filter by relationship type

    Returns:
        Graph data for visualization
    """
    try:
        # For now, use the same service but could be extended for filtering
        graph_data = await get_knowledge_graph(limit)
        return graph_data

    except Exception as e:
        logger.error(f"Error getting knowledge graph for visualization: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting knowledge graph for visualization: {str(e)}"
        )

@router.post("/knowledge-graph/query")
async def query_graph(query: GraphQuery):
    """
    Execute a Cypher query.

    Args:
        query: Cypher query and parameters

    Returns:
        Query results
    """
    try:
        result = await execute_graph_query(query.query, query.params)
        return result

    except Exception as e:
        logger.error(f"Error executing graph query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error executing graph query: {str(e)}"
        )

@router.get("/knowledge-graph/taxonomy", response_model=Taxonomy)
async def get_graph_taxonomy():
    """
    Get the taxonomy of entities.

    Returns:
        Taxonomy
    """
    try:
        taxonomy = await get_taxonomy()
        return taxonomy

    except Exception as e:
        logger.error(f"Error getting taxonomy: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting taxonomy: {str(e)}"
        )

@router.post("/knowledge-graph/relationship")
async def create_relationship(
    source_uuid: str = Body(...),
    target_uuid: str = Body(...),
    relationship_type: str = Body(...),
    properties: Optional[Dict[str, Any]] = Body(None)
):
    """
    Create a relationship between two entities.

    Args:
        source_uuid: Source entity UUID
        target_uuid: Target entity UUID
        relationship_type: Relationship type
        properties: Relationship properties

    Returns:
        Success status
    """
    try:
        result = await create_entity_relationship(source_uuid, target_uuid, relationship_type, properties)

        if result:
            return {"success": True, "message": "Relationship created successfully"}
        else:
            return {"success": False, "message": "Failed to create relationship"}

    except Exception as e:
        logger.error(f"Error creating relationship: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error creating relationship: {str(e)}"
        )

@router.get("/relationship-types")
async def get_relationship_types():
    """
    Get all relationship types in the knowledge graph.

    Returns:
        List of relationship types
    """
    try:
        adapter = await get_falkordb_adapter()

        # Query to get all relationship types
        query = """
        MATCH ()-[r]->()
        RETURN DISTINCT type(r) AS relationship_type
        """

        result = adapter.execute_cypher(query)

        relationship_types = []
        if result and len(result) > 1:
            for row in result[1]:
                relationship_types.append(row[0])

        return {"relationship_types": relationship_types}

    except Exception as e:
        logger.error(f"Error getting relationship types: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting relationship types: {str(e)}"
        )
