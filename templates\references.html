<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>References - Graphiti Knowledge Graph</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph References</h1>

        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">References</li>
            </ol>
        </nav>

        <!-- References -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Document References</h5>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <div class="row mb-4" id="reference-filters">
                    <div class="col-md-4">
                        <label for="document-selector" class="form-label">Document</label>
                        <select class="form-select" id="document-selector">
                            <option value="">All Documents</option>
                        </select>
                    </div>
                    <div class="col-md-8">
                        <label for="reference-search-input" class="form-label">Search</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="reference-search-input" placeholder="Search references...">
                            <button class="btn btn-primary" type="button" id="search-references-button">
                                <i class="bi bi-search"></i> Search
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="export-references-button" disabled>
                                <i class="bi bi-download"></i> Export CSV
                            </button>
                            <button class="btn btn-outline-primary" type="button" id="visualize-references-button" disabled>
                                <i class="bi bi-graph-up"></i> Visualize
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="row mb-4" id="reference-statistics">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6>Reference Statistics</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h3 id="references-count">0</h3>
                                                <p class="mb-0">Total References</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h3 id="documents-count">0</h3>
                                                <p class="mb-0">Documents</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h3 id="journals-count">0</h3>
                                                <p class="mb-0">Journals</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h3 id="authors-count">0</h3>
                                                <p class="mb-0">Authors</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading indicator -->
                <div id="references-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading references...</p>
                </div>

                <!-- References list -->
                <div id="references-list">
                    <!-- References will be displayed here -->
                </div>

                <!-- Visualization -->
                <div id="reference-visualization" style="display: none;">
                    <!-- Visualization will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="static/js/graphiti_ui.js"></script>
    <script src="static/js/graphiti_ui_part3.js"></script>
    <script src="static/js/graphiti_ui_part4.js"></script>
    <script src="static/js/reference_display.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the references page
            if (typeof initializeReferenceDisplay === 'function') {
                // Use the enhanced reference display if available
                initializeReferenceDisplay();
            } else {
                // Fall back to the original initialization
                initializeReferencesTab();
            }
        });
    </script>
</body>
</html>
