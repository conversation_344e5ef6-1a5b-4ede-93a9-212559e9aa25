<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
        }
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .score-badge {
            float: right;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
        }
        .vis-network {
            outline: none;
        }
        #dropzone {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        #dropzone.dragover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }
        .file-item {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        #answer-sources {
            max-height: 300px;
            overflow-y: auto;
        }
        .reference-area {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin-top: 10px;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
        .metadata-card {
            margin-bottom: 15px;
        }
        .metadata-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metadata-value {
            margin-bottom: 10px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        .user-message {
            background-color: #e9ecef;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .assistant-message {
            background-color: #f0f7ff;
            margin-right: auto;
            border-top-left-radius: 0;
        }
        .message-container {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-header {
            text-align: right;
            color: #495057;
        }
        .assistant-header {
            color: #0d6efd;
        }
        .sources-toggle {
            margin-top: 5px;
            text-align: right;
        }
        .sources-container {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Knowledge Graph Explorer</h1>
        
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="true">Search</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="answer-tab" data-bs-toggle="tab" data-bs-target="#answer" type="button" role="tab" aria-controls="answer" aria-selected="false">Answer Questions</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab" aria-controls="documents" aria-selected="false">Documents</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="entities-tab" data-bs-toggle="tab" data-bs-target="#entities" type="button" role="tab" aria-controls="entities" aria-selected="false">Entities</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="enhancements-tab" data-bs-toggle="tab" data-bs-target="#enhancements" type="button" role="tab" aria-controls="enhancements" aria-selected="false">Enhancements</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="graph-tab" data-bs-toggle="tab" data-bs-target="#graph" type="button" role="tab" aria-controls="graph" aria-selected="false">Knowledge Graph</button>
            </li>
        </ul>
        
        <!-- Tab Content -->
        <div class="tab-content" id="mainTabsContent">
            <!-- Search Tab -->
            <div class="tab-pane fade show active" id="search" role="tabpanel" aria-labelledby="search-tab">
                <!-- Search content here -->
            </div>
            
            <!-- Answer Questions Tab -->
            <div class="tab-pane fade" id="answer" role="tabpanel" aria-labelledby="answer-tab">
                <div class="row mb-4">
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="text" id="question-input" class="form-control" placeholder="Ask a question...">
                            <button class="btn btn-primary" type="button" id="question-button">Ask</button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" type="button" id="clear-chat-button">Clear Chat</button>
                    </div>
                </div>

                <div id="conversation-container" class="mb-4" style="max-height: 500px; overflow-y: auto;">
                    <div id="conversation-history"></div>
                </div>

                <div id="answer-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Generating answer...</p>
                </div>

                <div id="answer-sources" style="display: none;">
                    <h5>Sources:</h5>
                    <div id="sources-list"></div>
                </div>
            </div>
            
            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                <!-- Upload content here -->
            </div>
            
            <!-- Documents Tab -->
            <div class="tab-pane fade" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                <!-- Documents content here -->
            </div>
            
            <!-- Entities Tab -->
            <div class="tab-pane fade" id="entities" role="tabpanel" aria-labelledby="entities-tab">
                <!-- Entities content here -->
            </div>
            
            <!-- Enhancements Tab -->
            <div class="tab-pane fade" id="enhancements" role="tabpanel" aria-labelledby="enhancements-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Reference Enhancements</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Deduplication</h5>
                                        <p class="card-text">Find and link duplicate references in the knowledge graph.</p>
                                        <button id="deduplicate-button" class="btn btn-primary">Run Deduplication</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Citation Network</h5>
                                        <p class="card-text">Build a citation network from references in the knowledge graph.</p>
                                        <button id="build-network-button" class="btn btn-primary">Build Network</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Bibliographic Enrichment</h5>
                                        <p class="card-text">Enrich references with data from external bibliographic databases.</p>
                                        <button id="enrich-references-button" class="btn btn-primary">Enrich References</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Run All</h5>
                                        <p class="card-text">Run all reference enhancements in sequence.</p>
                                        <button id="run-all-enhancements-button" class="btn btn-primary">Run All</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="enhancements-status">
                            <!-- Status will be displayed here -->
                        </div>
                        
                        <div id="enhancements-progress" style="display: none;">
                            <h5 class="progress-message">Processing...</h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <div id="enhancements-results">
                            <!-- Results will be displayed here -->
                        </div>
                        
                        <div id="network-visualization">
                            <!-- Network visualization will be displayed here -->
                        </div>
                        
                        <div id="duplicate-groups-list">
                            <!-- Duplicate groups will be displayed here -->
                        </div>
                        
                        <div id="enriched-references-list">
                            <!-- Enriched references will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Graph Tab -->
            <div class="tab-pane fade" id="graph" role="tabpanel" aria-labelledby="graph-tab">
                <!-- Graph content here -->
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/conversation.js"></script>
    <script src="/static/search.js"></script>
    <script src="/static/entities.js"></script>
    <script src="/static/enhanced_upload.js"></script>
    <script src="/static/metadata.js"></script>
    <script src="/static/references.js"></script>
    <script src="/static/reference_enhancements.js"></script>
    <script src="/static/graph.js"></script>
</body>
</html>
