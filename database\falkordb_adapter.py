"""
FalkorDB adapter for the Graphiti application.
"""

import redis
from typing import List, Dict, Any, Optional, Union, Tuple

from utils.config import FALKORDB_HOST, FALKORDB_PORT, FALKORDB_PASSWORD, FALKORDB_GRAPH
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Singleton adapter instance
_falkordb_adapter = None

async def get_falkordb_adapter():
    """
    Get the FalkorDB adapter instance.

    Returns:
        FalkorDB adapter instance
    """
    global _falkordb_adapter

    if _falkordb_adapter is None:
        _falkordb_adapter = FalkorDBAdapter()

    return _falkordb_adapter

class FalkorDBAdapter:
    """
    Adapter for FalkorDB operations.
    """

    def __init__(self, graph_name: str = FALKORDB_GRAPH):
        """
        Initialize the FalkorDB adapter.

        Args:
            graph_name: Name of the graph
        """
        self.graph_name = graph_name
        self.redis_client = None
        self.connect()
        logger.info(f"Initialized FalkorDB adapter with graph: {graph_name}")

    def connect(self):
        """
        Connect to the FalkorDB database.
        """
        try:
            # First try connecting with password if provided
            if FALKORDB_PASSWORD:
                try:
                    self.redis_client = redis.Redis(
                        host=FALKORDB_HOST,
                        port=FALKORDB_PORT,
                        password=FALKORDB_PASSWORD,
                        decode_responses=True
                    )
                    # Test connection
                    self.redis_client.ping()
                    logger.info(f"Connected to FalkorDB at {FALKORDB_HOST}:{FALKORDB_PORT} with password")
                except Exception as e:
                    logger.warning(f"Failed to connect with password: {e}")
                    # If authentication fails, try connecting without password
                    self.redis_client = None

            # If no password provided or connection with password failed, try without password
            if not self.redis_client:
                self.redis_client = redis.Redis(
                    host=FALKORDB_HOST,
                    port=FALKORDB_PORT,
                    decode_responses=True
                )
                # Test connection
                self.redis_client.ping()
                logger.info(f"Connected to FalkorDB at {FALKORDB_HOST}:{FALKORDB_PORT} without password")
        except Exception as e:
            logger.error(f"Error connecting to FalkorDB: {e}")
            self.redis_client = None

    def close(self):
        """
        Close the connection to the FalkorDB database.
        """
        if self.redis_client:
            self.redis_client.close()
            self.redis_client = None
            logger.info("Closed connection to FalkorDB")

    def is_connected(self) -> bool:
        """
        Check if the adapter is connected to the database.

        Returns:
            True if connected, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            return self.redis_client.ping()
        except:
            return False

    def execute_cypher(self, query: str, params: Dict[str, Any] = None) -> List[Any]:
        """
        Execute a Cypher query.

        Args:
            query: Cypher query
            params: Query parameters

        Returns:
            Query results
        """
        if not self.is_connected():
            self.connect()
            if not self.is_connected():
                logger.error("Cannot execute query: not connected to FalkorDB")
                return []

        try:
            # If we have parameters, replace them in the query
            if params:
                # Replace parameters in the query
                for key, value in params.items():
                    param_placeholder = f"${key}"
                    if isinstance(value, str):
                        # Escape single quotes in string values
                        value = value.replace("'", "\\'")
                        # Wrap string values in single quotes
                        query = query.replace(param_placeholder, f"'{value}'")
                    elif value is None:
                        # Replace None with NULL
                        query = query.replace(param_placeholder, "NULL")
                    else:
                        # For non-string values, just convert to string
                        query = query.replace(param_placeholder, str(value))

            # Prepare the command
            command = ["GRAPH.QUERY", self.graph_name, query]

            # Execute the command
            result = self.redis_client.execute_command(*command)
            return result
        except Exception as e:
            logger.error(f"Error executing Cypher query: {e}")
            return []

    def create_node(self, labels: Union[str, List[str]], properties: Dict[str, Any]) -> Optional[str]:
        """
        Create a node in the graph.

        Args:
            labels: Node label(s)
            properties: Node properties

        Returns:
            UUID of the created node, or None if creation failed
        """
        # Convert single label to list
        if isinstance(labels, str):
            labels = [labels]

        # Create label string
        label_str = ':'.join(labels)

        # Create properties string
        props_list = []
        for key, value in properties.items():
            if isinstance(value, str):
                props_list.append(f"{key}: '{value}'")
            else:
                props_list.append(f"{key}: {value}")

        props_str = ', '.join(props_list)

        # Create query
        query = f"CREATE (n:{label_str} {{{props_str}}}) RETURN n.uuid as uuid"

        # Execute query
        result = self.execute_cypher(query)

        # Check result
        if result and len(result) > 1 and len(result[1]) > 0:
            return result[1][0][0]

        return None

    def create_relationship(self, from_uuid: str, to_uuid: str, rel_type: str, properties: Dict[str, Any] = None) -> bool:
        """
        Create a relationship between two nodes.

        Args:
            from_uuid: UUID of the source node
            to_uuid: UUID of the target node
            rel_type: Relationship type
            properties: Relationship properties

        Returns:
            True if the relationship was created, False otherwise
        """
        # Create properties string
        props_str = ""
        if properties:
            props_list = []
            for key, value in properties.items():
                if isinstance(value, str):
                    props_list.append(f"{key}: '{value}'")
                else:
                    props_list.append(f"{key}: {value}")

            props_str = f" {{{', '.join(props_list)}}}"

        # Create query
        query = f"""
        MATCH (a), (b)
        WHERE a.uuid = '{from_uuid}' AND b.uuid = '{to_uuid}'
        CREATE (a)-[r:{rel_type}{props_str}]->(b)
        RETURN r
        """

        # Execute query
        result = self.execute_cypher(query)

        # Check result
        return result and len(result) > 1 and len(result[1]) > 0

    def get_node_by_uuid(self, uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get a node by its UUID.

        Args:
            uuid: UUID of the node

        Returns:
            Node properties, or None if the node was not found
        """
        query = f"""
        MATCH (n {{uuid: '{uuid}'}})
        RETURN n
        """

        result = self.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            return result[1][0][0]

        return None

    def get_nodes_by_label(self, label: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get nodes by label.

        Args:
            label: Node label
            limit: Maximum number of nodes to return

        Returns:
            List of node properties
        """
        query = f"""
        MATCH (n:{label})
        RETURN n
        LIMIT {limit}
        """

        result = self.execute_cypher(query)

        if result and len(result) > 1:
            return [row[0] for row in result[1]]

        return []

# For backward compatibility
GraphitiFalkorDBAdapter = FalkorDBAdapter