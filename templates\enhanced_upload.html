<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Document Upload - Graphiti</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .enhanced-dropzone {
            border: 3px dashed #0d6efd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .enhanced-dropzone:hover {
            border-color: #0b5ed7;
            background-color: #e7f1ff;
        }
        
        .enhanced-dropzone.drag-over {
            border-color: #198754;
            background-color: #d1e7dd;
        }
        
        .file-item {
            transition: all 0.2s ease;
        }
        
        .file-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .progress-container {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .preview-container {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .settings-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .text-purple {
            color: #6f42c1 !important;
        }
        
        .processing-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
        }
        
        .feature-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-light vh-100 p-4">
                <h4 class="mb-4">
                    <i class="bi bi-graph-up text-primary"></i>
                    Graphiti
                </h4>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="/">
                        <i class="bi bi-house"></i> Dashboard
                    </a>
                    <a class="nav-link active" href="/enhanced-upload">
                        <i class="bi bi-cloud-upload"></i> Enhanced Upload
                    </a>
                    <a class="nav-link" href="/documents">
                        <i class="bi bi-files"></i> Documents
                    </a>
                    <a class="nav-link" href="/entities">
                        <i class="bi bi-diagram-3"></i> Entities
                    </a>
                    <a class="nav-link" href="/search">
                        <i class="bi bi-search"></i> Search
                    </a>
                    <a class="nav-link" href="/qa">
                        <i class="bi bi-chat-dots"></i> Q&A
                    </a>
                </nav>
                
                <!-- Processing Settings -->
                <div class="settings-panel mt-4">
                    <h6><i class="bi bi-gear"></i> Processing Settings</h6>
                    
                    <div class="mb-3">
                        <label for="chunk-size" class="form-label">Chunk Size</label>
                        <input type="number" class="form-control form-control-sm" id="chunk-size" value="1200" min="100" max="5000">
                        <small class="text-muted">Characters per chunk</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="overlap" class="form-label">Overlap</label>
                        <input type="number" class="form-control form-control-sm" id="overlap" value="0" min="0" max="500">
                        <small class="text-muted">Character overlap between chunks</small>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="extract-entities" checked>
                        <label class="form-check-label" for="extract-entities">
                            Extract Entities
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="extract-references" checked>
                        <label class="form-check-label" for="extract-references">
                            Extract References
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="extract-metadata" checked>
                        <label class="form-check-label" for="extract-metadata">
                            Extract Metadata
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="generate-embeddings" checked>
                        <label class="form-check-label" for="generate-embeddings">
                            Generate Embeddings
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="bi bi-cloud-upload text-primary"></i> Enhanced Document Upload</h2>
                        <p class="text-muted">Upload and process multiple document types with real-time progress tracking</p>
                    </div>
                    <div class="processing-stats text-center">
                        <div class="row">
                            <div class="col">
                                <h5 class="mb-0" id="active-operations">0</h5>
                                <small>Active</small>
                            </div>
                            <div class="col">
                                <h5 class="mb-0" id="completed-operations">0</h5>
                                <small>Completed</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Alert Container -->
                <div id="alert-container"></div>
                
                <!-- Features Overview -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card feature-card text-center">
                            <div class="card-body">
                                <i class="bi bi-files fs-1 text-primary"></i>
                                <h6 class="mt-2">Multiple Formats</h6>
                                <small class="text-muted">PDF, Word, Excel, PowerPoint, Text, HTML, Images</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card feature-card text-center">
                            <div class="card-body">
                                <i class="bi bi-eye fs-1 text-success"></i>
                                <h6 class="mt-2">Live Preview</h6>
                                <small class="text-muted">Preview content before processing</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card feature-card text-center">
                            <div class="card-body">
                                <i class="bi bi-speedometer2 fs-1 text-warning"></i>
                                <h6 class="mt-2">Real-time Progress</h6>
                                <small class="text-muted">Track processing step-by-step</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card feature-card text-center">
                            <div class="card-body">
                                <i class="bi bi-robot fs-1 text-info"></i>
                                <h6 class="mt-2">Enhanced OCR</h6>
                                <small class="text-muted">Mistral OCR with fallback methods</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Upload Area -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-upload"></i> Document Upload</h5>
                    </div>
                    <div class="card-body">
                        <!-- Drop Zone -->
                        <div id="enhanced-dropzone" class="enhanced-dropzone mb-3">
                            <div class="dropzone-content">
                                <i class="bi bi-cloud-upload fs-1 text-primary"></i>
                                <h5>Drag & Drop Documents Here</h5>
                                <p class="text-muted">Supports PDF, Word, Excel, PowerPoint, Text, HTML, and Images</p>
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('enhanced-file-input').click()">
                                    <i class="bi bi-file-earmark-plus"></i> Select Files
                                </button>
                            </div>
                        </div>
                        
                        <!-- Hidden File Input -->
                        <input type="file" id="enhanced-file-input" class="d-none" multiple 
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.md,.html,.htm,.xml,.jpg,.jpeg,.png,.gif,.tiff,.bmp">
                        
                        <!-- File List -->
                        <div id="enhanced-file-list"></div>
                    </div>
                </div>
                
                <!-- Progress Container -->
                <div id="enhanced-progress-container" class="progress-container"></div>
                
                <!-- Preview Container -->
                <div id="enhanced-preview-container" class="preview-container"></div>
                
                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="bi bi-question-circle"></i> Supported Document Types</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <h6><i class="bi bi-file-earmark-pdf text-danger"></i> Documents</h6>
                                <ul class="list-unstyled small">
                                    <li>PDF (.pdf)</li>
                                    <li>Word (.doc, .docx)</li>
                                    <li>Text (.txt, .md)</li>
                                    <li>HTML (.html, .htm)</li>
                                </ul>
                            </div>
                            <div class="col-md-3">
                                <h6><i class="bi bi-file-earmark-excel text-success"></i> Spreadsheets</h6>
                                <ul class="list-unstyled small">
                                    <li>Excel (.xls, .xlsx)</li>
                                    <li>CSV (.csv)</li>
                                </ul>
                            </div>
                            <div class="col-md-3">
                                <h6><i class="bi bi-file-earmark-ppt text-warning"></i> Presentations</h6>
                                <ul class="list-unstyled small">
                                    <li>PowerPoint (.ppt, .pptx)</li>
                                </ul>
                            </div>
                            <div class="col-md-3">
                                <h6><i class="bi bi-file-earmark-image text-purple"></i> Images</h6>
                                <ul class="list-unstyled small">
                                    <li>JPEG (.jpg, .jpeg)</li>
                                    <li>PNG (.png)</li>
                                    <li>TIFF (.tiff, .tif)</li>
                                    <li>BMP (.bmp)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i>
                            <strong>Processing Pipeline:</strong>
                            Each document goes through text extraction, chunking, entity extraction, 
                            reference extraction, and embedding generation. Progress is tracked in real-time 
                            with detailed step information.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Enhanced Upload JS -->
    <script src="/static/js/enhanced_upload.js"></script>
    
    <script>
        // Update operation counters
        function updateOperationCounters() {
            if (window.enhancedUploader) {
                document.getElementById('active-operations').textContent = enhancedUploader.activeOperations.size;
            }
        }
        
        // Update counters every 5 seconds
        setInterval(updateOperationCounters, 5000);
    </script>
</body>
</html>
