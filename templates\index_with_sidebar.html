<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti Knowledge Graph Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
            overflow-x: hidden;
        }
        /* Sidebar styles */
        .app-container {
            display: flex;
            width: 100%;
            min-height: calc(100vh - 40px);
        }

        #sidebar {
            width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: all 0.3s;
            height: calc(100vh - 40px);
            position: sticky;
            top: 20px;
            overflow-y: auto;
        }

        #sidebar.collapsed {
            width: 60px;
        }

        #sidebar.collapsed .sidebar-nav-item span {
            display: none;
        }

        #main-content {
            flex: 1;
            transition: all 0.3s;
            padding: 0 15px;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h4 {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-nav {
            padding: 15px 0;
        }

        .sidebar-nav-item {
            padding: 10px 15px;
            display: flex;
            align-items: center;
            color: #495057;
            text-decoration: none;
            cursor: pointer;
        }

        .sidebar-nav-item:hover {
            background-color: #e9ecef;
        }

        .sidebar-nav-item.active {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .sidebar-nav-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Content styles */
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #0d6efd;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .score-badge {
            float: right;
            margin-left: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
        }
        .vis-network {
            outline: none;
        }
        #dropzone {
            border: 2px dashed #0d6efd;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        #dropzone.dragover {
            background-color: #e3f2fd;
            border-color: #0d6efd;
        }
        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .dropzone-content i {
            font-size: 48px;
            color: #0d6efd;
            margin-bottom: 15px;
        }
        .file-item {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        #answer-sources {
            max-height: 300px;
            overflow-y: auto;
        }
        .reference-area {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin-top: 10px;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
        .metadata-card {
            margin-bottom: 15px;
        }
        .metadata-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metadata-value {
            margin-bottom: 10px;
        }
        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .user-message {
            background-color: #e9ecef;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .assistant-message {
            background-color: #f0f7ff;
            margin-right: auto;
            border-top-left-radius: 0;
        }
        .message-container {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-content p {
            margin-bottom: 0.5rem;
        }
        .message-content p:last-child {
            margin-bottom: 0;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-header {
            text-align: right;
            color: #495057;
        }
        .assistant-header {
            color: #0d6efd;
        }
        .sources-toggle {
            margin-top: 5px;
            text-align: right;
        }
        .sources-container {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        #answer-loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        #answer-sources {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        /* Settings panel styles */
        .settings-panel {
            display: none;
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div id="sidebar">
            <div class="sidebar-header">
                <h4>Graphiti</h4>
                <button id="sidebar-toggle" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-chevron-left"></i>
                </button>
            </div>
            <div class="sidebar-nav">
                <div class="sidebar-nav-item" data-target="chat">
                    <i class="bi bi-chat-dots"></i>
                    <span>Chat</span>
                </div>
                <div class="sidebar-nav-item" data-target="search">
                    <i class="bi bi-search"></i>
                    <span>Search</span>
                </div>
                <div class="sidebar-nav-item" data-target="entities">
                    <i class="bi bi-diagram-3"></i>
                    <span>Entities</span>
                </div>
                <div class="sidebar-nav-item" data-target="upload">
                    <i class="bi bi-cloud-upload"></i>
                    <span>Upload</span>
                </div>
                <div class="sidebar-nav-item" data-target="metadata">
                    <i class="bi bi-file-earmark-text"></i>
                    <span>Metadata</span>
                </div>
                <div class="sidebar-nav-item" data-target="references">
                    <i class="bi bi-journal-text"></i>
                    <span>References</span>
                </div>
                <div class="sidebar-nav-item" data-target="enhancements">
                    <i class="bi bi-stars"></i>
                    <span>Enhancements</span>
                </div>
                <div class="sidebar-nav-item" data-target="graph">
                    <i class="bi bi-graph-up"></i>
                    <span>Graph</span>
                </div>
                <div class="sidebar-nav-item" data-target="settings">
                    <i class="bi bi-gear"></i>
                    <span>Settings</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <div class="container">
                <h1 class="mb-4">Graphiti Knowledge Graph Explorer</h1>

                <!-- Tab Content -->
                <div class="tab-content" id="mainTabsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/conversation.js"></script>
    <script src="/static/search.js"></script>
    <script src="/static/entities.js"></script>
    <script src="/static/upload.js"></script>
    <script src="/static/metadata.js"></script>
    <script src="/static/references.js"></script>
    <script src="/static/graph_visualization.js"></script>
    <script src="/static/settings.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar navigation
            const sidebarItems = document.querySelectorAll('.sidebar-nav-item');
            const mainContent = document.getElementById('mainTabsContent');
            
            // Function to load tab content
            function loadTabContent(tabId) {
                // Hide all tabs
                document.querySelectorAll('.tab-pane').forEach(tab => {
                    tab.style.display = 'none';
                });
                
                // Show selected tab
                const selectedTab = document.getElementById(tabId);
                if (selectedTab) {
                    selectedTab.style.display = 'block';
                } else {
                    // Load content via AJAX if not already loaded
                    fetch(`/api/content/${tabId}`)
                        .then(response => response.text())
                        .then(html => {
                            const newTab = document.createElement('div');
                            newTab.id = tabId;
                            newTab.className = 'tab-pane fade show active';
                            newTab.innerHTML = html;
                            mainContent.appendChild(newTab);
                        })
                        .catch(error => {
                            console.error(`Error loading ${tabId} content:`, error);
                        });
                }
            }
            
            // Add click event to sidebar items
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Remove active class from all items
                    sidebarItems.forEach(i => i.classList.remove('active'));
                    
                    // Add active class to clicked item
                    this.classList.add('active');
                    
                    // Load tab content
                    const tabId = this.getAttribute('data-target');
                    loadTabContent(tabId);
                });
            });
            
            // Toggle sidebar
            document.getElementById('sidebar-toggle').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('collapsed');
                
                // Change icon based on state
                const icon = this.querySelector('i');
                if (document.getElementById('sidebar').classList.contains('collapsed')) {
                    icon.classList.remove('bi-chevron-left');
                    icon.classList.add('bi-chevron-right');
                } else {
                    icon.classList.remove('bi-chevron-right');
                    icon.classList.add('bi-chevron-left');
                }
            });
            
            // Activate the first item by default
            sidebarItems[0].click();
        });
    </script>
</body>
</html>
