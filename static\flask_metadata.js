/**
 * Metadata handling for Graphiti Flask Web Interface
 * 
 * This script handles the metadata functionality in the Metadata tab.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Flask Metadata.js loaded");
    
    // Load metadata when the metadata tab is shown
    document.getElementById('metadata-tab').addEventListener('click', function() {
        loadMetadata();
    });
});

/**
 * Load metadata from the server
 */
function loadMetadata() {
    // Show loading indicator
    document.getElementById('metadata-loading').style.display = 'block';
    
    // Clear previous results
    document.getElementById('metadata-list').innerHTML = '';
    
    // Call the API to get documents
    fetch('/api/documents')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(documents => {
            // Hide loading indicator
            document.getElementById('metadata-loading').style.display = 'none';
            
            // Display the metadata
            displayMetadata(documents);
        })
        .catch(error => {
            console.error('Error:', error);
            
            // Hide loading indicator
            document.getElementById('metadata-loading').style.display = 'none';
            
            // Display an error message
            document.getElementById('metadata-list').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    Error loading metadata: ${error.message}
                </div>
            `;
        });
}

/**
 * Display metadata
 * 
 * @param {Array} documents - The documents to display
 */
function displayMetadata(documents) {
    // Get the metadata container
    const metadataContainer = document.getElementById('metadata-list');
    
    // If there are no documents, display a message
    if (!documents || documents.length === 0) {
        metadataContainer.innerHTML = `
            <div class="alert alert-info" role="alert">
                No documents found.
            </div>
        `;
        return;
    }
    
    // Create a row for the documents
    const row = document.createElement('div');
    row.className = 'row';
    
    // Add each document to the row
    documents.forEach(document => {
        // Create a column for the document
        const col = document.createElement('div');
        col.className = 'col-md-6 mb-4';
        
        // Create a card for the document
        const card = document.createElement('div');
        card.className = 'card h-100';
        
        // Create a card header with the document title
        const header = document.createElement('div');
        header.className = 'card-header';
        header.innerHTML = `
            <h5 class="mb-0">${document.title || document.filename || 'Unknown Document'}</h5>
        `;
        
        // Create a card body for the metadata
        const body = document.createElement('div');
        body.className = 'card-body';
        
        // Add metadata fields
        body.innerHTML = `
            <div class="metadata-card">
                <div class="metadata-title">Filename</div>
                <div class="metadata-value">${document.filename || 'Unknown'}</div>
                
                <div class="metadata-title">Date Added</div>
                <div class="metadata-value">${document.date_added || 'Unknown'}</div>
                
                <div class="metadata-title">Size</div>
                <div class="metadata-value">${document.size ? formatFileSize(document.size) : 'Unknown'}</div>
                
                <div class="metadata-title">Chunks</div>
                <div class="metadata-value">${document.chunks || 0}</div>
                
                <div class="metadata-title">Entities</div>
                <div class="metadata-value">${document.entities || 0}</div>
            </div>
        `;
        
        // Add the header and body to the card
        card.appendChild(header);
        card.appendChild(body);
        
        // Add the card to the column
        col.appendChild(card);
        
        // Add the column to the row
        row.appendChild(col);
    });
    
    // Add the row to the container
    metadataContainer.appendChild(row);
}

/**
 * Format file size in a human-readable format
 * 
 * @param {number} bytes - The file size in bytes
 * @returns {string} The formatted file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
