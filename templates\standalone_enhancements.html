<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reference Enhancements - Graphiti</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
        }
        .navbar {
            margin-bottom: 20px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light bg-light rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="/standalone-enhancements">Reference Enhancements</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <div class="p-4 mb-4 bg-light rounded-3">
            <div class="container-fluid py-3">
                <h1 class="display-5 fw-bold">Reference Enhancements</h1>
                <p class="col-md-8 fs-4">Enhance your knowledge graph with advanced reference management features.</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Deduplication</h5>
                        <p class="card-text">Find and link duplicate references in the knowledge graph.</p>
                        <button id="deduplicate-button" class="btn btn-primary">Run Deduplication</button>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Citation Network</h5>
                        <p class="card-text">Build a citation network from references in the knowledge graph.</p>
                        <button id="build-network-button" class="btn btn-primary">Build Network</button>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Bibliographic Enrichment</h5>
                        <p class="card-text">Enrich references with data from external bibliographic databases.</p>
                        <button id="enrich-references-button" class="btn btn-primary">Enrich References</button>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Run All</h5>
                        <p class="card-text">Run all reference enhancements in sequence.</p>
                        <button id="run-all-enhancements-button" class="btn btn-primary">Run All</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="enhancements-status">
            <!-- Status will be displayed here -->
        </div>
        
        <div id="enhancements-progress" style="display: none;">
            <h5 class="progress-message">Processing...</h5>
            <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div>
        
        <div id="enhancements-results">
            <!-- Results will be displayed here -->
        </div>
        
        <div id="network-visualization">
            <!-- Network visualization will be displayed here -->
        </div>
        
        <div id="duplicate-groups-list">
            <!-- Duplicate groups will be displayed here -->
        </div>
        
        <div id="enriched-references-list">
            <!-- Enriched references will be displayed here -->
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/reference_enhancements.js"></script>
</body>
</html>
