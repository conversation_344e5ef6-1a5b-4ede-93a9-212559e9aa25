/**
 * Format entity name by converting LaTeX notation to Unicode
 */
function formatEntityName(name) {
    if (!name) return 'Unknown';

    // Convert LaTeX-style notation to Unicode
    const replacements = {
        '$eta$': 'β',
        '$alpha$': 'α',
        '$beta$': 'β',
        '$gamma$': 'γ',
        '$delta$': 'δ',
        '$epsilon$': 'ε',
        '$zeta$': 'ζ',
        '$theta$': 'θ',
        '$lambda$': 'λ',
        '$mu$': 'μ',
        '$nu$': 'ν',
        '$pi$': 'π',
        '$rho$': 'ρ',
        '$sigma$': 'σ',
        '$tau$': 'τ',
        '$phi$': 'φ',
        '$chi$': 'χ',
        '$psi$': 'ψ',
        '$omega$': 'ω'
    };

    let formattedName = name;
    for (const [latex, unicode] of Object.entries(replacements)) {
        formattedName = formattedName.replace(new RegExp(latex.replace('$', '\\$'), 'gi'), unicode);
    }

    return formattedName;
}

// Entities tab functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load entities when tab is clicked
    document.getElementById('entities-tab').addEventListener('click', function() {
        loadEntities();
    });

    // Search entities when search button is clicked
    document.getElementById('entity-search-button').addEventListener('click', function() {
        searchEntities();
    });

    // Search entities when type filter is changed
    document.getElementById('entity-type-filter').addEventListener('change', function() {
        searchEntities();
    });

    // Sort entities when sort filter is changed
    document.getElementById('entity-sort-filter').addEventListener('change', function() {
        // If we already have entities loaded, just re-sort and display them
        const entitiesList = document.getElementById('entities-list');
        if (entitiesList.innerHTML !== '') {
            sortAndDisplayEntities();
        } else {
            searchEntities();
        }
    });

    // Update mention count value display when slider is moved
    document.getElementById('mention-count-slider').addEventListener('input', function() {
        document.getElementById('mention-count-value').textContent = this.value;
    });

    // Apply mention count filter when button is clicked
    document.getElementById('apply-mention-filter').addEventListener('click', function() {
        sortAndDisplayEntities();
    });

    // Global variable to store the current entities
    let currentEntities = [];

    // Function to load all entities
    function loadEntities() {
        document.getElementById('entities-loading').style.display = 'block';
        document.getElementById('entities-list').innerHTML = '';
        document.getElementById('entity-details').style.display = 'none';

        fetch('/api/fast/entities?limit=50')
            .then(response => response.json())
            .then(data => {
                document.getElementById('entities-loading').style.display = 'none';
                console.log("Entities data:", data); // Debug log

                // Check if data.entities exists and has length property
                if (!data.entities || !Array.isArray(data.entities) || data.entities.length === 0) {
                    document.getElementById('entities-list').innerHTML = '<div class="alert alert-info">No entities found. Try extracting entities first.</div>';
                    return;
                }

                // Ensure all entities have the required properties
                const validEntities = data.entities.filter(entity => {
                    return entity && entity.name && entity.type;
                }).map(entity => {
                    // Ensure mention_count is a number
                    if (typeof entity.mention_count !== 'number') {
                        entity.mention_count = 0;
                    }
                    return entity;
                });

                if (validEntities.length === 0) {
                    document.getElementById('entities-list').innerHTML = '<div class="alert alert-info">No valid entities found. Try extracting entities first.</div>';
                    return;
                }

                // Store the entities globally
                currentEntities = validEntities;
                console.log("Valid entities:", validEntities.length); // Debug log

                // Sort and display entities
                sortAndDisplayEntities();
            })
            .catch(error => {
                console.error("Error loading entities:", error); // Debug log
                document.getElementById('entities-loading').style.display = 'none';
                document.getElementById('entities-list').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            });
    }

    // Function to search entities
    function searchEntities() {
        const query = document.getElementById('entity-search-input').value.trim();
        const entityType = document.getElementById('entity-type-filter').value;

        document.getElementById('entities-loading').style.display = 'block';
        document.getElementById('entities-list').innerHTML = '';
        document.getElementById('entity-details').style.display = 'none';

        let url = '/api/fast/entities?limit=50';
        if (query || entityType) {
            url += '?';
            if (query) {
                url += `q=${encodeURIComponent(query)}`;
            }
            if (entityType) {
                url += query ? `&type=${encodeURIComponent(entityType)}` : `type=${encodeURIComponent(entityType)}`;
            }
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                document.getElementById('entities-loading').style.display = 'none';
                console.log("Search entities data:", data); // Debug log

                // Check if data.entities exists and has length property
                if (!data.entities || !Array.isArray(data.entities) || data.entities.length === 0) {
                    document.getElementById('entities-list').innerHTML = '<div class="alert alert-info">No entities found matching your criteria.</div>';
                    return;
                }

                // Ensure all entities have the required properties
                const validEntities = data.entities.filter(entity => {
                    return entity && entity.name && entity.type;
                }).map(entity => {
                    // Ensure mention_count is a number
                    if (typeof entity.mention_count !== 'number') {
                        entity.mention_count = 0;
                    }
                    return entity;
                });

                if (validEntities.length === 0) {
                    document.getElementById('entities-list').innerHTML = '<div class="alert alert-info">No valid entities found matching your criteria.</div>';
                    return;
                }

                // Store the entities globally
                currentEntities = validEntities;
                console.log("Valid search entities:", validEntities.length); // Debug log

                // Sort and display entities
                sortAndDisplayEntities();
            })
            .catch(error => {
                console.error("Error searching entities:", error); // Debug log
                document.getElementById('entities-loading').style.display = 'none';
                document.getElementById('entities-list').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            });
    }

    // Function to sort and display entities based on current filters
    function sortAndDisplayEntities() {
        if (!currentEntities || currentEntities.length === 0) {
            return;
        }

        // Get the sort option
        const sortOption = document.getElementById('entity-sort-filter').value;

        // Get the minimum mention count
        const minMentionCount = parseInt(document.getElementById('mention-count-slider').value);

        // Filter entities by mention count
        let filteredEntities = currentEntities.filter(entity => {
            return entity.mention_count >= minMentionCount;
        });

        // Sort entities based on the selected option
        if (sortOption === 'mentions-desc') {
            filteredEntities.sort((a, b) => b.mention_count - a.mention_count);
        } else if (sortOption === 'mentions-asc') {
            filteredEntities.sort((a, b) => a.mention_count - b.mention_count);
        }

        // Display the filtered and sorted entities
        displayEntities(filteredEntities);

        // Update the entity frequency chart
        updateEntityFrequencyChart(filteredEntities);
    }

    // Function to display entities
    function displayEntities(entities) {
        // Group entities by type
        const entityTypes = {};
        entities.forEach(entity => {
            if (!entityTypes[entity.type]) {
                entityTypes[entity.type] = [];
            }
            entityTypes[entity.type].push(entity);
        });

        let entitiesHtml = '';

        // Add summary of filtered entities
        const minMentionCount = parseInt(document.getElementById('mention-count-slider').value);
        const sortOption = document.getElementById('entity-sort-filter').value;
        let sortDescription = 'sorted by name';
        if (sortOption === 'mentions-desc') {
            sortDescription = 'sorted by mentions (high to low)';
        } else if (sortOption === 'mentions-asc') {
            sortDescription = 'sorted by mentions (low to high)';
        }

        entitiesHtml += `
            <div class="alert alert-info mb-4">
                <h5>Entities Summary</h5>
                <p>Showing ${entities.length} entities with ${minMentionCount}+ mentions, ${sortDescription}</p>
                <div class="row">
        `;

        Object.keys(entityTypes).sort().forEach(type => {
            entitiesHtml += `
                <div class="col-md-3 mb-2">
                    <a href="#entity-type-${type.replace(/\s+/g, '-')}" class="badge bg-primary p-2" style="text-decoration: none;">
                        ${type} (${entityTypes[type].length})
                    </a>
                </div>
            `;
        });

        entitiesHtml += `
                </div>
            </div>
        `;

        // Create a card for each entity type
        Object.keys(entityTypes).sort().forEach(type => {
            const typeEntities = entityTypes[type];
            const typeId = `entity-type-${type.replace(/\s+/g, '-')}`;

            entitiesHtml += `
                <div class="card mb-4" id="${typeId}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">${type} (${typeEntities.length})</h5>
                        <a href="#" class="btn btn-sm btn-outline-secondary">Back to Top</a>
                    </div>
                    <div class="card-body">
                        <div class="row">
            `;

            // Create a badge for each entity with mention count
            // Note: We don't sort here as the entities are already sorted by sortAndDisplayEntities
            typeEntities.forEach(entity => {
                // Calculate badge color based on mention count
                let badgeColor = 'primary';
                if (entity.mention_count > 20) {
                    badgeColor = 'danger';
                } else if (entity.mention_count > 10) {
                    badgeColor = 'warning';
                } else if (entity.mention_count > 5) {
                    badgeColor = 'success';
                } else if (entity.mention_count > 2) {
                    badgeColor = 'info';
                }

                entitiesHtml += `
                    <div class="col-md-4 mb-2">
                        <span class="badge bg-light text-dark p-2 entity-badge"
                              style="cursor: pointer; display: inline-block; width: 100%; text-align: left; overflow: hidden; text-overflow: ellipsis;"
                              data-uuid="${entity.uuid}" title="${entity.name} (${entity.mention_count} mentions)">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-truncate" style="max-width: 85%;">${formatEntityName(entity.name)}</span>
                                <span class="badge bg-${badgeColor} rounded-pill ms-1">${entity.mention_count}</span>
                            </div>
                        </span>
                    </div>
                `;
            });

            entitiesHtml += `
                        </div>
                    </div>
                </div>
            `;
        });

        document.getElementById('entities-list').innerHTML = entitiesHtml;

        // Add event listeners to entity badges
        document.querySelectorAll('.entity-badge').forEach(badge => {
            badge.addEventListener('click', function() {
                const uuid = this.getAttribute('data-uuid');
                showEntityDetails(uuid);
            });
        });
    }

    // Function to update the entity frequency chart
    function updateEntityFrequencyChart(entities) {
        // Clear the chart placeholder
        document.getElementById('chart-placeholder').style.display = 'none';

        // Check if entities is undefined or empty
        if (!entities || entities.length === 0) {
            document.getElementById('chart-placeholder').style.display = 'block';
            document.getElementById('chart-placeholder').innerHTML = '<p>No entities found for the current filters</p>';
            return;
        }

        // Get the selected entity type
        const entityType = document.getElementById('entity-type-filter').value;

        // If no specific entity type is selected, show top 10 entities by mention count
        let chartEntities = entities;
        let chartTitle = 'Top 10 Entities by Mention Count';

        if (entityType) {
            // Filter entities by the selected type
            chartEntities = entities.filter(entity => entity.type === entityType);
            chartTitle = `${entityType} Entities by Mention Count`;
        }

        // Sort entities by mention count (descending)
        chartEntities.sort((a, b) => b.mention_count - a.mention_count);

        // Take top 10 entities
        chartEntities = chartEntities.slice(0, 10);

        // If no entities to display, show a message
        if (chartEntities.length === 0) {
            document.getElementById('chart-placeholder').style.display = 'block';
            document.getElementById('chart-placeholder').innerHTML = '<p>No entities found for the current filters</p>';
            return;
        }

        // Prepare data for the chart
        const labels = chartEntities.map(entity => entity.name);
        const data = chartEntities.map(entity => entity.mention_count);
        const backgroundColors = chartEntities.map(entity => {
            // Generate colors based on mention count
            if (entity.mention_count > 20) {
                return 'rgba(220, 53, 69, 0.7)'; // danger
            } else if (entity.mention_count > 10) {
                return 'rgba(255, 193, 7, 0.7)'; // warning
            } else if (entity.mention_count > 5) {
                return 'rgba(25, 135, 84, 0.7)'; // success
            } else if (entity.mention_count > 2) {
                return 'rgba(13, 202, 240, 0.7)'; // info
            } else {
                return 'rgba(13, 110, 253, 0.7)'; // primary
            }
        });

        // Get the chart canvas
        const chartCanvas = document.getElementById('entity-frequency-chart');

        // Check if a chart already exists and destroy it
        if (window.entityChart) {
            window.entityChart.destroy();
        }

        // Create the chart
        window.entityChart = new Chart(chartCanvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Mention Count',
                    data: data,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle,
                        font: {
                            size: 14
                        }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Mentions: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Mention Count'
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    }

    // Function to show entity details
    function showEntityDetails(uuid) {
        document.getElementById('entities-loading').style.display = 'block';
        document.getElementById('entity-details').style.display = 'none';

        fetch(`/api/entities/${uuid}`)
            .then(response => response.json())
            .then(entity => {
                document.getElementById('entities-loading').style.display = 'none';
                document.getElementById('entity-details').style.display = 'block';

                // Display entity details
                document.getElementById('entity-name').textContent = formatEntityName(entity.name);
                document.getElementById('entity-type').textContent = entity.type;
                document.getElementById('entity-description').textContent = entity.description || 'No description available';

                // Display entity mentions
                let mentionsHtml = '';
                if (entity.mentions && entity.mentions.length > 0) {
                    entity.mentions.forEach(mention => {
                        mentionsHtml += `
                            <div class="card mb-2">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">${mention.document}</h6>
                                    <p class="card-text">${mention.preview}</p>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    mentionsHtml = '<p class="text-muted">No mentions found.</p>';
                }
                document.getElementById('entity-mentions').innerHTML = mentionsHtml;

                // Display entity relationships
                let relationshipsHtml = '';
                if (entity.relationships && entity.relationships.length > 0) {
                    entity.relationships.forEach(rel => {
                        relationshipsHtml += `
                            <div class="card mb-2">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2">
                                        <span class="badge bg-info">${rel.type}</span>
                                        <span class="badge bg-secondary">${rel.entity.type}</span>
                                        ${rel.entity.name}
                                    </h6>
                                    <p class="card-text">${rel.description || 'No description'}</p>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    relationshipsHtml = '<p class="text-muted">No relationships found.</p>';
                }
                document.getElementById('entity-relationships').innerHTML = relationshipsHtml;

                // Scroll to entity details
                document.getElementById('entity-details').scrollIntoView({ behavior: 'smooth' });
            })
            .catch(error => {
                document.getElementById('entities-loading').style.display = 'none';
                alert(`Error loading entity details: ${error.message}`);
            });
    }
});
