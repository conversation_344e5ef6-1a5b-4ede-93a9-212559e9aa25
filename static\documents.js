// Documents tab functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load documents when tab is clicked
    document.getElementById('documents-tab').addEventListener('click', function() {
        loadDocuments();
    });

    // Function to load documents
    function loadDocuments() {
        document.getElementById('documents-loading').style.display = 'block';
        document.getElementById('documents-list').innerHTML = '';

        fetch('/api/documents')
            .then(response => response.json())
            .then(data => {
                document.getElementById('documents-loading').style.display = 'none';

                if (data.documents.length === 0) {
                    document.getElementById('documents-list').innerHTML = '<div class="alert alert-info">No documents found.</div>';
                    return;
                }

                let documentsHtml = '<div class="list-group">';
                data.documents.forEach(doc => {
                    documentsHtml += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">${doc.name}</h5>
                                <small>${doc.chunks} chunks</small>
                            </div>
                            <p class="mb-1">${doc.description || 'No description'}</p>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <small>UUID: ${doc.uuid}</small>
                                <button class="btn btn-sm btn-danger delete-document" data-uuid="${doc.uuid}" data-name="${doc.name}">Delete</button>
                            </div>
                        </div>
                    `;
                });
                documentsHtml += '</div>';

                document.getElementById('documents-list').innerHTML = documentsHtml;

                // Add event listeners to delete buttons
                document.querySelectorAll('.delete-document').forEach(button => {
                    button.addEventListener('click', function() {
                        const uuid = this.getAttribute('data-uuid');
                        const name = this.getAttribute('data-name');

                        if (confirm(`Are you sure you want to delete the document "${name}"? This action cannot be undone.`)) {
                            deleteDocument(uuid);
                        }
                    });
                });
            })
            .catch(error => {
                document.getElementById('documents-loading').style.display = 'none';
                document.getElementById('documents-list').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            });
    }

    // Function to delete a document
    function deleteDocument(uuid) {
        document.getElementById('documents-loading').style.display = 'block';

        fetch(`/api/documents/${uuid}`, {
            method: 'DELETE'
        })
            .then(response => response.json())
            .then(data => {
                document.getElementById('documents-loading').style.display = 'none';

                if (data.success) {
                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        ${data.message} (${data.deleted_facts} facts removed)
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;

                    const documentsTab = document.getElementById('documents');
                    documentsTab.insertBefore(alertDiv, documentsTab.firstChild);

                    // Reload the documents list
                    loadDocuments();

                    // Auto-dismiss the alert after 5 seconds
                    setTimeout(() => {
                        alertDiv.classList.remove('show');
                        setTimeout(() => alertDiv.remove(), 150);
                    }, 5000);
                } else {
                    alert(`Error: ${data.message || 'Unknown error'}`);
                }
            })
            .catch(error => {
                document.getElementById('documents-loading').style.display = 'none';
                alert(`Error deleting document: ${error.message}`);
            });
    }
});
