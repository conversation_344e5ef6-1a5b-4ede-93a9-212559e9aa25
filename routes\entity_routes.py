"""
Entity-related routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException, Query, Depends, BackgroundTasks, Body
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)

# Entity update model
class EntityUpdate(BaseModel):
    name: str = Field(..., description="Entity name")
    type: str = Field(..., description="Entity type")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0-1)")

from services.entity_service import (
    extract_entities_from_document,
    get_entity_types,
    get_entity_counts,
    get_entities_by_type,
    get_entity_by_uuid,
    get_entity_relationships
)
from database.database_service import get_falkordb_adapter
from models.entity import Entity, EntitySummary, EntityTypeCounts
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["entities"])

@router.post("/entities/extract/{document_id}")
async def extract_entities(
    document_id: str,
    background_tasks: BackgroundTasks = None,
    llm_provider: str = "openai"
):
    """
    Extract entities from a document.

    Args:
        document_id: Document ID
        background_tasks: Background tasks
        llm_provider: LLM provider to use

    Returns:
        Extraction result
    """
    try:
        if background_tasks:
            # Add entity extraction as a background task
            background_tasks.add_task(
                extract_entities_from_document,
                document_id,
                llm_provider
            )

            return {
                "message": "Entity extraction started in the background",
                "document_id": document_id
            }
        else:
            # Extract entities synchronously
            result = await extract_entities_from_document(document_id, llm_provider)
            return result

    except Exception as e:
        logger.error(f"Error extracting entities: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting entities: {str(e)}"
        )

@router.get("/entity-types")
async def get_all_entity_types():
    """
    Get all entity types.

    Returns:
        List of entity types
    """
    try:
        entity_types = await get_entity_types()
        return {"entity_types": entity_types}

    except Exception as e:
        logger.error(f"Error getting entity types: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entity types: {str(e)}"
        )

@router.get("/entity-counts", response_model=EntityTypeCounts)
async def get_all_entity_counts():
    """
    Get counts of entities by type.

    Returns:
        Entity type counts
    """
    try:
        counts = await get_entity_counts()
        return counts

    except Exception as e:
        logger.error(f"Error getting entity counts: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entity counts: {str(e)}"
        )

@router.get("/graph-stats")
async def get_graph_statistics():
    """
    Get statistics about the knowledge graph.

    Returns:
        Dictionary with various statistics about the graph
    """
    try:
        # Get database adapter
        adapter = await get_falkordb_adapter()

        # Get document count (Episodes in FalkorDB)
        doc_query = "MATCH (d:Episode) RETURN count(d) as count"
        doc_result = adapter.execute_cypher(doc_query)
        document_count = doc_result[1][0][0] if len(doc_result) > 1 and len(doc_result[1]) > 0 else 0

        # Get entity count
        entity_query = "MATCH (e:Entity) RETURN count(e) as count"
        entity_result = adapter.execute_cypher(entity_query)
        entity_count = entity_result[1][0][0] if len(entity_result) > 1 and len(entity_result[1]) > 0 else 0

        # Get relationship count
        rel_query = "MATCH ()-[r]->() RETURN count(r) as count"
        rel_result = adapter.execute_cypher(rel_query)
        relationship_count = rel_result[1][0][0] if len(rel_result) > 1 and len(rel_result[1]) > 0 else 0

        # Get entity type count
        type_query = "MATCH (e:Entity) RETURN count(DISTINCT e.type) as count"
        type_result = adapter.execute_cypher(type_query)
        entity_type_count = type_result[1][0][0] if len(type_result) > 1 and len(type_result[1]) > 0 else 0

        # Get entity counts by type
        entity_counts = await get_entity_counts()

        return {
            "total_episodes": document_count,
            "total_entities": entity_count,
            "total_relationships": relationship_count,
            "total_references": 0,  # Will be implemented later
            "entity_type_count": entity_type_count,
            "entity_counts_by_type": entity_counts
        }
    except Exception as e:
        logger.error(f"Error getting graph statistics: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting graph statistics: {str(e)}"
        )

@router.get("/entities")
async def get_all_entities(
    limit: int = Query(1000, ge=1, le=5000),  # Increased maximum limit to 5000
    offset: int = Query(0, ge=0),
    entity_type: Optional[str] = None,
    min_mentions: int = Query(0, ge=0)
):
    """
    Get all entities or filter by type.

    Args:
        limit: Maximum number of entities to return
        offset: Offset for pagination
        entity_type: Optional entity type to filter by
        min_mentions: Minimum number of mentions

    Returns:
        List of entities
    """
    try:
        # Get database adapter
        adapter = await get_falkordb_adapter()

        # First, get accurate counts for all entity types
        count_query = """
        MATCH (e:Entity)
        RETURN e.type as type, count(e) as count
        ORDER BY count DESC
        """

        count_result = adapter.execute_cypher(count_query)

        # Process count results
        type_counts = {}
        total_count = 0

        if count_result and len(count_result) > 1:
            for row in count_result[1]:
                try:
                    type_val = row[0]
                    count_val = row[1]

                    # Handle list values
                    if isinstance(type_val, list) and len(type_val) > 0:
                        type_val = str(type_val[0])

                    # Convert to string to ensure it's hashable
                    type_val = str(type_val) if type_val else "Unknown"

                    type_counts[type_val] = count_val
                    total_count += count_val
                except Exception as e:
                    logger.warning(f"Error processing count row: {str(e)}")
                    continue

        # Build query based on parameters
        if entity_type:
            # For specific entity types, check if we need to get all entities of that type
            type_count = type_counts.get(entity_type, 0)

            # If the entity type has fewer entities than the limit, get all of them
            if type_count <= 5000:  # Safety limit to prevent excessive queries
                # Query for all entities of this type without pagination
                query = f"""
                MATCH (e:Entity)
                WHERE e.type = '{entity_type}'
                OPTIONAL MATCH (f:Fact)-[:MENTIONS]->(e)
                WITH e, count(f) as mentions
                RETURN e.uuid as uuid, e.name as name, e.type as type, mentions
                ORDER BY e.name
                """
                # Skip pagination for this case
                offset = 0
                limit = type_count
            else:
                # Query for specific entity type with pagination
                query = f"""
                MATCH (e:Entity)
                WHERE e.type = '{entity_type}'
                OPTIONAL MATCH (f:Fact)-[:MENTIONS]->(e)
                WITH e, count(f) as mentions
                RETURN e.uuid as uuid, e.name as name, e.type as type, mentions
                ORDER BY e.name
                SKIP {offset}
                LIMIT {limit}
                """

            # Get total count for this entity type
            filtered_count = type_count
        else:
            # Query for all entity types
            query = f"""
            MATCH (e:Entity)
            OPTIONAL MATCH (f:Fact)-[:MENTIONS]->(e)
            WITH e, count(f) as mentions
            RETURN e.uuid as uuid, e.name as name, e.type as type, mentions
            ORDER BY e.name
            SKIP {offset}
            LIMIT {limit}
            """

            # Use total count for all entities
            filtered_count = total_count

        # Execute query
        result = adapter.execute_cypher(query)

        # Process results
        entities_list = []
        if result and len(result) > 1:
            for row in result[1]:
                try:
                    # Extract entity data directly from query results
                    uuid_val = row[0]
                    name_val = row[1]
                    type_val = row[2]
                    mentions_val = row[3] if len(row) > 3 else 1  # Get mentions count from query result

                    # Handle list values
                    if isinstance(uuid_val, list) and len(uuid_val) > 0:
                        uuid_val = str(uuid_val[0])
                    if isinstance(name_val, list) and len(name_val) > 0:
                        name_val = str(name_val[0])
                    if isinstance(type_val, list) and len(type_val) > 0:
                        type_val = str(type_val[0])

                    # Convert to string to ensure it's hashable
                    type_val = str(type_val) if type_val else "Unknown"

                    # Create entity dictionary
                    entity_dict = {
                        "uuid": str(uuid_val) if uuid_val else "",
                        "name": str(name_val) if name_val else "",
                        "type": type_val,
                        "confidence": 1.0,
                        "mention_count": max(1, int(mentions_val))  # Use actual mention count, minimum 1
                    }

                    entities_list.append(entity_dict)
                except Exception as e:
                    logger.warning(f"Error processing entity row: {str(e)}")
                    continue

        # Add pagination metadata
        pagination = {
            "limit": limit,
            "offset": offset,
            "total": filtered_count,
            "page": (offset // limit) + 1 if limit > 0 else 1,
            "total_pages": (filtered_count + limit - 1) // limit if limit > 0 else 1,
            "has_next": (offset + limit) < filtered_count,
            "has_prev": offset > 0
        }

        return {
            "entities": entities_list,
            "count": filtered_count,  # Use the accurate count
            "entity_type": entity_type or "all",
            "type_counts": type_counts,  # Include counts for all entity types
            "pagination": pagination  # Include pagination metadata
        }

    except Exception as e:
        logger.error(f"Error getting entities: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entities: {str(e)}"
        )

@router.get("/entities/{entity_type}")
async def get_entities_by_type_endpoint(
    entity_type: str,
    limit: int = Query(5000, ge=1, le=10000),  # Increased limit for specific entity types
    offset: int = Query(0, ge=0),
    all: bool = Query(False)  # New parameter to get all entities of this type
):
    """
    Get entities by type.

    Args:
        entity_type: Entity type
        limit: Maximum number of entities to return
        offset: Offset for pagination
        all: Whether to get all entities of this type (ignores limit and offset)

    Returns:
        List of entities
    """
    try:
        if all:
            # If all=true, get all entities of this type
            # First, get the count of entities of this type
            adapter = await get_falkordb_adapter()

            count_query = f"""
            MATCH (e:Entity)
            WHERE e.type = '{entity_type}'
            RETURN count(e) as count
            """

            count_result = adapter.execute_cypher(count_query)

            if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
                count = count_result[1][0][0]
                # Use the count as the limit to get all entities
                return await get_all_entities(limit=count, offset=0, entity_type=entity_type)

        # Otherwise, redirect to the main entities endpoint with the entity_type parameter
        return await get_all_entities(limit=limit, offset=offset, entity_type=entity_type)

    except Exception as e:
        logger.error(f"Error getting entities by type: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entities by type: {str(e)}"
        )

@router.get("/entity/{entity_uuid}")
async def get_entity(entity_uuid: str):
    """
    Get an entity by UUID.

    Args:
        entity_uuid: Entity UUID

    Returns:
        Entity details
    """
    try:
        # Get the entity using the async function
        entity_obj = await get_entity_by_uuid(entity_uuid)

        if not entity_obj:
            raise HTTPException(
                status_code=404,
                detail=f"Entity not found: {entity_uuid}"
            )

        # Convert entity object to dictionary
        entity = {
            "uuid": entity_obj.uuid,
            "name": entity_obj.name,
            "type": entity_obj.type,
            "confidence": entity_obj.confidence
        }

        # Get relationships
        relationships = await get_entity_relationships(entity_uuid)

        # If None, use an empty list
        if relationships is None:
            relationships = []

        # Get associated facts (to be implemented)
        facts = []

        # Get source documents (to be implemented)
        documents = []

        return {
            "entity": entity,
            "relationships": relationships,
            "facts": facts,
            "documents": documents
        }

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Error getting entity: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entity: {str(e)}"
        )

@router.put("/entity/{entity_uuid}")
async def update_entity(entity_uuid: str, entity_update: EntityUpdate):
    """
    Update an entity.

    Args:
        entity_uuid: Entity UUID
        entity_update: Updated entity data

    Returns:
        Updated entity
    """
    try:
        # Check if entity exists
        entity = await get_entity_by_uuid(entity_uuid)

        if not entity:
            raise HTTPException(
                status_code=404,
                detail=f"Entity not found: {entity_uuid}"
            )

        # Get database adapter
        adapter = await get_falkordb_adapter()

        # Update entity
        query = f"""
        MATCH (e:Entity {{uuid: '{entity_uuid}'}})
        SET e.name = '{entity_update.name}',
            e.type = '{entity_update.type}',
            e.confidence = {entity_update.confidence}
        RETURN e
        """

        result = adapter.execute_cypher(query)

        if not result or len(result) <= 1 or len(result[1]) == 0:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to update entity: {entity_uuid}"
            )

        # Get updated entity
        updated_entity = await get_entity_by_uuid(entity_uuid)

        return {
            "message": "Entity updated successfully",
            "entity": updated_entity
        }

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Error updating entity: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error updating entity: {str(e)}"
        )
