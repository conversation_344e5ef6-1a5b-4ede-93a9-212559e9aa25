/**
 * Document metadata display functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const metadataTab = document.getElementById('metadata-tab');
    const documentSelect = document.getElementById('metadata-document-select');
    const metadataContent = document.getElementById('metadata-content');
    const metadataLoading = document.getElementById('metadata-loading');
    
    // Metadata fields
    const metadataTitle = document.getElementById('metadata-title');
    const metadataAuthors = document.getElementById('metadata-authors');
    const metadataDate = document.getElementById('metadata-date');
    const metadataJournal = document.getElementById('metadata-journal');
    const metadataDoi = document.getElementById('metadata-doi');
    const metadataKeywords = document.getElementById('metadata-keywords');
    const metadataFilename = document.getElementById('metadata-filename');
    const metadataFilesize = document.getElementById('metadata-filesize');
    const metadataPages = document.getElementById('metadata-pages');
    const metadataCreator = document.getElementById('metadata-creator');
    const metadataProducer = document.getElementById('metadata-producer');
    const metadataCreationDate = document.getElementById('metadata-creation-date');
    const metadataAbstract = document.getElementById('metadata-abstract');
    
    // Current document ID
    let currentDocumentId = null;
    
    // Initialize
    if (metadataTab) {
        metadataTab.addEventListener('click', loadDocuments);
    }
    
    if (documentSelect) {
        documentSelect.addEventListener('change', function() {
            currentDocumentId = this.value;
            if (currentDocumentId) {
                loadMetadata(currentDocumentId);
            } else {
                clearMetadata();
            }
        });
    }
    
    // Load documents
    async function loadDocuments() {
        try {
            const response = await fetch('/api/documents');
            const data = await response.json();
            
            if (response.ok) {
                updateDocumentSelect(data.documents);
            } else {
                showAlert('Error loading documents: ' + data.error, 'danger');
            }
        } catch (error) {
            console.error('Error loading documents:', error);
            showAlert('Error loading documents: ' + error.message, 'danger');
        }
    }
    
    // Update document select
    function updateDocumentSelect(documents) {
        if (!documentSelect) return;
        
        // Clear select
        documentSelect.innerHTML = '<option value="">Select a document</option>';
        
        // Add documents with metadata
        const documentsWithMetadata = documents.filter(doc => doc.has_metadata);
        
        if (documentsWithMetadata.length === 0) {
            documentSelect.innerHTML += '<option value="" disabled>No documents with metadata found</option>';
            return;
        }
        
        documentsWithMetadata.forEach(doc => {
            const option = document.createElement('option');
            option.value = doc.uuid;
            
            // Use title if available, otherwise use filename
            const displayName = doc.title || doc.name;
            option.textContent = displayName;
            
            documentSelect.appendChild(option);
        });
        
        // If there was a previously selected document, try to select it again
        if (currentDocumentId) {
            documentSelect.value = currentDocumentId;
            
            // If the document is no longer available, clear the selection
            if (documentSelect.value !== currentDocumentId) {
                currentDocumentId = null;
                clearMetadata();
            } else {
                // Reload metadata
                loadMetadata(currentDocumentId);
            }
        }
    }
    
    // Load metadata for a document
    async function loadMetadata(documentId) {
        try {
            // Show loading
            metadataContent.style.display = 'none';
            metadataLoading.style.display = 'block';
            
            const response = await fetch(`/api/document/${documentId}`);
            const data = await response.json();
            
            if (response.ok) {
                displayMetadata(data);
            } else {
                showAlert('Error loading metadata: ' + data.error, 'danger');
                clearMetadata();
            }
        } catch (error) {
            console.error('Error loading metadata:', error);
            showAlert('Error loading metadata: ' + error.message, 'danger');
            clearMetadata();
        } finally {
            metadataLoading.style.display = 'none';
            metadataContent.style.display = 'block';
        }
    }
    
    // Display metadata
    function displayMetadata(data) {
        // Document information
        setTextContent(metadataTitle, data.metadata_title);
        setTextContent(metadataAuthors, data.metadata_authors);
        setTextContent(metadataDate, data.metadata_publication_date);
        setTextContent(metadataJournal, data.metadata_journal);
        
        // DOI with link
        if (data.metadata_doi) {
            const doiLink = document.createElement('a');
            doiLink.href = data.metadata_doi.startsWith('http') ? data.metadata_doi : `https://doi.org/${data.metadata_doi}`;
            doiLink.textContent = data.metadata_doi;
            doiLink.target = '_blank';
            
            metadataDoi.innerHTML = '';
            metadataDoi.appendChild(doiLink);
        } else {
            setTextContent(metadataDoi, '-');
        }
        
        setTextContent(metadataKeywords, data.metadata_keywords);
        
        // File information
        setTextContent(metadataFilename, data.name);
        setTextContent(metadataFilesize, formatFileSize(data.pdf_file_size_bytes));
        setTextContent(metadataPages, data.pdf_num_pages);
        setTextContent(metadataCreator, data.pdf_creator);
        setTextContent(metadataProducer, data.pdf_producer);
        setTextContent(metadataCreationDate, formatDate(data.pdf_creationdate));
        
        // Abstract
        setTextContent(metadataAbstract, data.metadata_abstract);
    }
    
    // Clear metadata
    function clearMetadata() {
        // Document information
        setTextContent(metadataTitle, '-');
        setTextContent(metadataAuthors, '-');
        setTextContent(metadataDate, '-');
        setTextContent(metadataJournal, '-');
        setTextContent(metadataDoi, '-');
        setTextContent(metadataKeywords, '-');
        
        // File information
        setTextContent(metadataFilename, '-');
        setTextContent(metadataFilesize, '-');
        setTextContent(metadataPages, '-');
        setTextContent(metadataCreator, '-');
        setTextContent(metadataProducer, '-');
        setTextContent(metadataCreationDate, '-');
        
        // Abstract
        setTextContent(metadataAbstract, '-');
    }
    
    // Helper function to set text content
    function setTextContent(element, value) {
        if (!element) return;
        
        element.textContent = value || '-';
    }
    
    // Format file size
    function formatFileSize(bytes) {
        if (!bytes) return '-';
        
        bytes = parseInt(bytes);
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Format date
    function formatDate(dateString) {
        if (!dateString) return '-';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        } catch (error) {
            return dateString;
        }
    }
    
    // Show alert
    function showAlert(message, type) {
        const alertContainer = document.getElementById('metadata-alert-container');
        if (!alertContainer) return;
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        alertContainer.appendChild(alert);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => {
                alert.remove();
            }, 150);
        }, 5000);
    }
    
    // Make refreshDocumentsList available globally
    window.refreshMetadataDocumentsList = loadDocuments;
});
