"""
Reference processing service for the Graphiti application.

This service handles reference extraction from documents using multiple methods.
"""

import os
import uuid
import logging
from typing import Dict, List, Any
from pathlib import Path

from reference_extraction import ReferenceExtractor
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Import Mistral OCR processor
try:
    from utils.mistral_ocr import MistralOCRProcessor
    logger.info("Successfully imported MistralOCRProcessor from utils.mistral_ocr")
except ImportError:
    logger.warning("Could not import MistralOCRProcessor")
    MistralOCRProcessor = None


class ReferenceProcessor:
    """Service for processing references from documents."""

    def __init__(self, llm_provider: str = 'openrouter'):
        """
        Initialize the reference processor.

        Args:
            llm_provider: LLM provider to use for reference extraction
        """
        self.reference_extractor = ReferenceExtractor(llm_provider=llm_provider)

        # Initialize Mistral OCR for enhanced reference extraction
        mistral_api_key = os.getenv('MISTRAL_API_KEY')
        logger.info(f"Mistral API key found: {'Yes' if mistral_api_key else 'No'}")
        if mistral_api_key and MistralOCRProcessor is not None:
            try:
                self.mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
                logger.info("✅ Successfully initialized Mistral OCR for enhanced reference extraction")
            except Exception as e:
                logger.error(f"❌ Could not initialize Mistral OCR: {e}", exc_info=True)
                self.mistral_ocr = None
        else:
            if not mistral_api_key:
                logger.warning("❌ No Mistral API key found, Mistral OCR will not be available for reference extraction")
            if MistralOCRProcessor is None:
                logger.warning("❌ MistralOCRProcessor not available, Mistral OCR will not be available for reference extraction")
            self.mistral_ocr = None

    async def extract_references_from_document(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references from a document using Mistral OCR as the primary method.

        Args:
            file_path: Path to the document file

        Returns:
            Result dictionary with reference extraction details
        """
        logger.info(f"Extracting references from document: {file_path}")

        try:
            # Use Mistral OCR as the primary reference extraction method
            logger.info(f"🔍 Checking Mistral OCR availability: {'Available' if self.mistral_ocr else 'Not Available'}")
            if self.mistral_ocr:
                logger.info("🚀 Starting Mistral OCR reference extraction...")
                result = await self._extract_references_with_mistral_ocr(file_path)
                if result.get("success", False) and result.get("total_reference_count", 0) > 0:
                    logger.info(f"✅ Mistral OCR extracted {result.get('total_reference_count', 0)} references")

                    # Update the master CSV file with the extracted references
                    await self._update_master_csv_with_references(file_path, result)

                    return result
                else:
                    logger.warning("⚠️ Mistral OCR did not extract any references, falling back to standard extraction")

            # Fall back to standard reference extraction
            logger.info("🔄 Using standard reference extraction as fallback...")
            result = await self.reference_extractor.process_document(file_path)

            if result.get("success", False):
                logger.info(f"✅ Standard extraction found {result.get('total_reference_count', 0)} references")

                # Update the master CSV file with the extracted references
                await self._update_master_csv_with_references(file_path, result)

                return result
            else:
                logger.warning("⚠️ Standard reference extraction failed")
                return {
                    "success": False,
                    "error": "All reference extraction methods failed",
                    "total_reference_count": 0,
                    "regex_references": [],
                    "llm_references": []
                }

        except Exception as e:
            logger.error(f"Error extracting references from document {file_path}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "total_reference_count": 0,
                "regex_references": [],
                "llm_references": []
            }

    async def _extract_references_with_mistral_ocr(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references using Mistral OCR.

        Args:
            file_path: Path to the document file

        Returns:
            Result dictionary with reference extraction details
        """
        try:
            logger.info(f"📄 Processing document with Mistral OCR: {file_path}")

            # Extract text from the document using Mistral OCR
            extracted_text = await self.mistral_ocr.extract_text_from_pdf(file_path)

            if not extracted_text:
                logger.error(f"❌ Mistral OCR processing failed: No text extracted from document")
                return {
                    "success": False,
                    "error": "No text extracted from document",
                    "total_reference_count": 0,
                    "regex_references": [],
                    "llm_references": []
                }

            logger.info(f"📝 Mistral OCR extracted {len(extracted_text)} characters of text")

            # Use regex-based reference extraction on the OCR text
            regex_references = self._extract_references_with_regex(extracted_text)

            # Format references as expected by the system with enhanced metadata
            formatted_references = []
            for ref_text in regex_references:
                # Extract metadata for each reference
                metadata = self._extract_reference_metadata(ref_text)

                formatted_references.append({
                    "text": ref_text,
                    "extraction_method": "regex",
                    "source": "mistral_ocr",
                    "metadata": metadata,
                    "uuid": str(uuid.uuid4())
                })

            # Get the references from the result
            references = formatted_references

            logger.info(f"📚 Found {len(references)} references in Mistral OCR text")

            # Format the result to match the expected structure
            result = {
                "success": True,
                "filename": Path(file_path).name,
                "file_path": file_path,
                "extraction_method": "mistral_ocr",
                "total_reference_count": len(references),
                "regex_references": references,  # All references are from regex extraction
                "llm_references": [],  # No LLM extraction in this flow
                "extracted_text_length": len(extracted_text),
                "ocr_confidence": 1.0  # Mistral OCR doesn't provide confidence scores
            }

            logger.info(f"✅ Mistral OCR reference extraction completed successfully")
            return result

        except Exception as e:
            logger.error(f"❌ Error in Mistral OCR reference extraction: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Mistral OCR error: {str(e)}",
                "total_reference_count": 0,
                "regex_references": [],
                "llm_references": []
            }

    async def save_references_to_files(self, references_result: Dict[str, Any], output_dir: str = None) -> Dict[str, str]:
        """
        Save references to JSON and CSV files.

        Args:
            references_result: Result from reference extraction
            output_dir: Directory to save files (optional)

        Returns:
            Dictionary with paths to saved files
        """
        try:
            file_paths = {}

            # Save to JSON
            json_path = self.reference_extractor.save_references_to_json(references_result, output_dir)
            if json_path:
                file_paths["json"] = json_path
                logger.info(f"📄 Saved references to JSON: {json_path}")

            # Save to CSV
            csv_path = self.reference_extractor.save_references_to_csv(references_result, output_dir)
            if csv_path:
                file_paths["csv"] = csv_path
                logger.info(f"📊 Saved references to CSV: {csv_path}")

            return file_paths

        except Exception as e:
            logger.error(f"Error saving references to files: {e}", exc_info=True)
            return {}

    def get_extraction_stats(self, references_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get statistics about the reference extraction.

        Args:
            references_result: Result from reference extraction

        Returns:
            Dictionary with extraction statistics
        """
        try:
            stats = {
                "success": references_result.get("success", False),
                "total_references": references_result.get("total_reference_count", 0),
                "regex_references": len(references_result.get("regex_references", [])),
                "llm_references": len(references_result.get("llm_references", [])),
                "extraction_method": references_result.get("extraction_method", "unknown"),
                "filename": references_result.get("filename", "unknown")
            }

            # Add OCR-specific stats if available
            if "ocr_confidence" in references_result:
                stats["ocr_confidence"] = references_result["ocr_confidence"]
            if "extracted_text_length" in references_result:
                stats["extracted_text_length"] = references_result["extracted_text_length"]

            return stats

        except Exception as e:
            logger.error(f"Error getting extraction stats: {e}")
            return {
                "success": False,
                "total_references": 0,
                "regex_references": 0,
                "llm_references": 0,
                "extraction_method": "error",
                "filename": "unknown"
            }

    async def _update_master_csv_with_references(self, file_path: str, references_result: Dict[str, Any]) -> None:
        """
        Update the master CSV file with extracted references.

        Args:
            file_path: Path to the document file
            references_result: Result from reference extraction
        """
        try:
            import csv
            import os
            from datetime import datetime

            # Path to the master CSV file
            csv_path = Path("references/all_references.csv")

            # Get references
            llm_references = references_result.get("llm_references", [])
            regex_references = references_result.get("regex_references", [])

            # If no references, return early
            if not llm_references and not regex_references:
                logger.info("No references to add to master CSV")
                return

            # Prepare new references to add
            new_references = []
            filename = Path(file_path).name
            extraction_date = datetime.now().isoformat()

            # Add regex references
            for ref in regex_references:
                # Try to parse the reference if it's a JSON string
                parsed_ref = self._parse_reference_text(ref)

                new_references.append({
                    "source_document": filename,
                    "document_uuid": "",
                    "extraction_method": "regex",
                    "reference_text": parsed_ref.get("text", ref),
                    "authors": parsed_ref.get("authors", ""),
                    "authors_raw": parsed_ref.get("authors", ""),
                    "title": parsed_ref.get("title", ""),
                    "year": parsed_ref.get("year", ""),
                    "journal": parsed_ref.get("journal", ""),
                    "volume": parsed_ref.get("volume", ""),
                    "issue": parsed_ref.get("issue", ""),
                    "pages": parsed_ref.get("pages", ""),
                    "doi": parsed_ref.get("doi", ""),
                    "pmid": parsed_ref.get("pmid", ""),
                    "url": parsed_ref.get("url", ""),
                    "citation_apa": "",
                    "citation_mla": "",
                    "citation_chicago": "",
                    "extraction_date": extraction_date,
                    "confidence_score": "0.6",
                    "confidence": "0.6"
                })

            # Add LLM references
            for ref in llm_references:
                # Convert authors list to string if needed
                authors = ref.get("authors", "")
                if isinstance(authors, list):
                    authors = ", ".join(authors)

                new_references.append({
                    "source_document": filename,
                    "document_uuid": "",
                    "extraction_method": "llm",
                    "reference_text": "",
                    "authors": authors,
                    "authors_raw": authors,
                    "title": ref.get("title", ""),
                    "year": ref.get("year", ""),
                    "journal": ref.get("journal", ref.get("conference", ref.get("book", ""))),
                    "volume": ref.get("volume", ""),
                    "issue": ref.get("issue", ""),
                    "pages": ref.get("pages", ""),
                    "doi": ref.get("doi", ""),
                    "pmid": ref.get("pmid", ""),
                    "url": ref.get("url", ""),
                    "citation_apa": "",
                    "citation_mla": "",
                    "citation_chicago": "",
                    "extraction_date": extraction_date,
                    "confidence_score": "0.8",
                    "confidence": "0.8"
                })

            # Define the fields for the CSV (matching existing structure)
            fields = [
                "source_document", "document_uuid", "extraction_method", "reference_text",
                "authors", "authors_raw", "title", "year", "journal", "volume", "issue", "pages",
                "doi", "pmid", "url", "citation_apa", "citation_mla", "citation_chicago",
                "extraction_date", "confidence_score", "confidence"
            ]

            # Read existing references and append new ones
            existing_references = []
            if os.path.exists(csv_path):
                with open(csv_path, 'r', encoding='utf-8', newline='') as f:
                    reader = csv.DictReader(f)
                    existing_references = list(reader)

            # Combine existing and new references
            all_references = existing_references + new_references

            # Write back to the master CSV
            with open(csv_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()
                writer.writerows(all_references)

            logger.info(f"✅ Successfully updated master CSV with {len(new_references)} references")

        except Exception as e:
            logger.error(f"❌ Error updating master CSV file: {e}", exc_info=True)

    def _parse_reference_text(self, ref_text: str) -> Dict[str, str]:
        """
        Parse reference text that might be a JSON string or plain text.

        Args:
            ref_text: Reference text to parse

        Returns:
            Dictionary with parsed reference fields
        """
        try:
            import json
            import re

            # If it's a JSON string, try to parse it
            if ref_text.startswith("{'") or ref_text.startswith('{"'):
                # Handle Python dict format
                if ref_text.startswith("{'"):
                    # Convert Python dict format to JSON
                    ref_text = ref_text.replace("'", '"')

                parsed = json.loads(ref_text)

                if isinstance(parsed, dict) and 'text' in parsed:
                    # Extract information from the text field
                    text = parsed['text']
                    result = {"text": text}

                    # Try to extract structured information from the text
                    # Extract authors (usually at the beginning)
                    author_match = re.match(r'^([^.]+(?:\s+[A-Z]{1,3}(?:,\s*[^.]+)*)?)\.\s*', text)
                    if author_match:
                        result["authors"] = author_match.group(1).strip()

                    # Extract year (4 digits)
                    year_match = re.search(r'\b(19|20)\d{2}\b', text)
                    if year_match:
                        result["year"] = year_match.group(0)

                    # Extract journal/publication (text between periods, often after year)
                    if year_match:
                        after_year = text[year_match.end():].strip()
                        if after_year.startswith(';'):
                            after_year = after_year[1:].strip()

                        # Look for journal name (usually before volume/issue)
                        journal_match = re.match(r'^([^;.]+)', after_year)
                        if journal_match:
                            result["journal"] = journal_match.group(1).strip()

                    # Extract volume and issue
                    vol_issue_match = re.search(r';(\d+)(?:\((\d+)\))?', text)
                    if vol_issue_match:
                        result["volume"] = vol_issue_match.group(1)
                        if vol_issue_match.group(2):
                            result["issue"] = vol_issue_match.group(2)

                    # Extract pages
                    pages_match = re.search(r':(\d+(?:-\d+)?)', text)
                    if pages_match:
                        result["pages"] = pages_match.group(1)

                    # Extract DOI
                    doi_match = re.search(r'doi:?\s*([^\s]+)', text, re.IGNORECASE)
                    if doi_match:
                        result["doi"] = doi_match.group(1)

                    return result

            # If not JSON or parsing failed, return as plain text
            return {"text": ref_text}

        except Exception as e:
            # If parsing fails, return as plain text
            return {"text": ref_text}

    def _extract_references_with_regex(self, text: str) -> List[str]:
        """
        Extract references from text using improved regex patterns.

        Args:
            text: Text to extract references from

        Returns:
            List of reference strings
        """
        import re

        references = []

        # Look for reference sections first
        ref_sections = self._find_reference_sections(text)

        if ref_sections:
            # Process each reference section
            for section in ref_sections:
                section_refs = self._extract_from_reference_section(section)
                references.extend(section_refs)
        else:
            # Fallback to general patterns if no reference section found
            references = self._extract_with_general_patterns(text)

        # Remove duplicates and filter out false positives
        unique_references = self._filter_and_deduplicate_references(references)

        return unique_references

    def _find_reference_sections(self, text: str) -> List[str]:
        """Find reference sections in the document."""
        import re

        # Common reference section headers
        ref_headers = [
            r'(?:^|\n)\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited)\s*(?:\n|$)',
            r'(?:^|\n)\s*\d+\.\s*(?:REFERENCES?|Bibliography)\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:References?|Bibliography)\s*(?:\n|$)'
        ]

        sections = []
        for header_pattern in ref_headers:
            matches = list(re.finditer(header_pattern, text, re.IGNORECASE | re.MULTILINE))
            for match in matches:
                # Extract text from header to end of document or next major section
                start = match.end()
                # Look for next major section (like "Appendix", "Acknowledgments", etc.)
                end_patterns = [
                    r'(?:\n|^)\s*(?:Appendix|Acknowledgments?|Author\s+Information|Supplementary|Figure|Table)\s*(?:\n|$)',
                    r'(?:\n|^)\s*\d+\.\s*(?:Appendix|Acknowledgments?)\s*(?:\n|$)'
                ]

                end = len(text)
                for end_pattern in end_patterns:
                    end_match = re.search(end_pattern, text[start:], re.IGNORECASE | re.MULTILINE)
                    if end_match:
                        end = start + end_match.start()
                        break

                section = text[start:end].strip()
                if len(section) > 50:  # Only consider substantial sections
                    sections.append(section)

        return sections

    def _extract_from_reference_section(self, section: str) -> List[str]:
        """Extract individual references from a reference section."""
        import re

        references = []

        # Split by numbered references (1., 2., etc.)
        numbered_refs = re.split(r'\n\s*(\d+)\.\s+', section)

        if len(numbered_refs) > 2:  # If we found numbered references
            for i in range(2, len(numbered_refs), 2):  # Skip first empty and process pairs
                if i < len(numbered_refs):
                    ref_text = numbered_refs[i].strip()
                    if len(ref_text) > 20 and self._is_valid_reference(ref_text):
                        references.append(ref_text)
        else:
            # Try other splitting methods
            # Split by author patterns at line start
            author_splits = re.split(r'\n(?=[A-Z][a-z]+(?:\s+[A-Z]{1,3})?(?:,\s*[A-Z])?)', section)
            for ref_text in author_splits:
                ref_text = ref_text.strip()
                if len(ref_text) > 20 and self._is_valid_reference(ref_text):
                    references.append(ref_text)

        return references

    def _extract_with_general_patterns(self, text: str) -> List[str]:
        """Extract references using general patterns when no reference section is found."""
        import re

        references = []

        # More specific patterns for academic references
        patterns = [
            # Complete journal citation: Author. Title. Journal Year;Volume:Pages
            r'[A-Z][a-z]+(?:\s+[A-Z]{1,3})?(?:,\s*[A-Z][a-z]+(?:\s+[A-Z]{1,3})?)*\.\s*[^.]{10,}\.\s*[A-Z][^.]{5,}\.\s*\d{4};\d+:\d+',

            # Book citation: Author. Title. Publisher, Year
            r'[A-Z][a-z]+(?:\s+[A-Z]{1,3})?(?:,\s*[A-Z][a-z]+)*\.\s*[^.]{10,}\.\s*[A-Z][^,]{5,},\s*\d{4}',

            # Journal mentions in italics or quotes
            r'\*([^*]+Journal[^*]+)\*',  # *Journal Name*
            r'"([^"]+Journal[^"]+)"',    # "Journal Name"
            r'_([^_]+Journal[^_]+)_',    # _Journal Name_

            # Journal mentions with common patterns
            r'(?:published\s+in\s+|in\s+the\s+)([A-Z][^.,]{10,}(?:Journal|Review|Magazine|Proceedings)[^.,]{0,50})',

            # DOI references
            r'doi:\s*10\.\d+/[^\s]+',

            # PubMed references
            r'PMID:\s*\d+',

            # URL references
            r'https?://[^\s,;)]+',

            # Author-year patterns (common in text citations)
            r'([A-Z][a-z]+(?:\s+et\s+al\.?)?\s+\(\d{4}\))',

            # Complete citations at end of document (more specific)
            r'([A-Z][a-z]+,\s*[A-Z][^,]{1,50},\s*[^,]{1,100},\s*[^,]{1,100},\s*[^,]{1,100},\s*[^,]{1,100},\s*[^,]{1,100},\s*[^,]{1,100},\s*\d{4},\s*[^,]{1,100})',

            # Simple journal name extraction (most reliable for this case)
            r'Journal\s+of\s+[^.,;]{5,50}(?:Journal|Review|Magazine|Society|Association|Institute)[^.,;]{0,50}',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                # Clean up the match
                if isinstance(match, tuple):
                    match = match[0] if match[0] else match[1] if len(match) > 1 else ""

                match = match.strip()

                # Clean up common formatting issues
                match = match.strip('*_"')  # Remove surrounding formatting

                # Skip if too long (likely captured too much text)
                if len(match) > 200:
                    continue

                # Skip if too short
                if len(match) < 10:
                    continue

                references.append(match)

        return references

    def _is_valid_reference(self, ref_text: str) -> bool:
        """Check if a reference text is likely a valid academic reference."""
        import re

        # Must have reasonable length
        if len(ref_text) < 10 or len(ref_text) > 1000:
            return False

        # Should not contain common false positive indicators
        false_positives = [
            r'page\s+\d+',  # Page numbers
            r'figure\s+\d+',  # Figure references
            r'table\s+\d+',  # Table references
            r'section\s+\d+',  # Section references
            r'chapter\s+\d+',  # Chapter references
            r'©\s*\d{4}',  # Copyright notices
            r'version\s+\d+',  # Version numbers
            r'^\s*\d+\s*$',  # Just numbers
            r'^\s*[a-z]+\s*$',  # Just lowercase words
        ]

        for pattern in false_positives:
            if re.search(pattern, ref_text, re.IGNORECASE):
                return False

        # Check for academic indicators (more flexible)
        academic_indicators = [
            r'\b(19|20)\d{2}\b',  # Year
            r'\bjournal\b',  # Journal
            r'\breview\b',  # Review
            r'\bproceedings\b',  # Proceedings
            r'\bdoi\b',  # DOI
            r'\bpmid\b',  # PMID
            r'\bvol\b',  # Volume
            r'\bissue\b',  # Issue
            r'\bpp\?\b',  # Pages
            r'[A-Z][a-z]+(?:\s+[A-Z]{1,3})?',  # Author-like patterns
            r'https?://',  # URLs
            r'et\s+al\.',  # Et al
        ]

        # Must have at least one academic indicator
        for indicator in academic_indicators:
            if re.search(indicator, ref_text, re.IGNORECASE):
                return True

        return False

    def _filter_and_deduplicate_references(self, references: List[str]) -> List[str]:
        """Filter out false positives and remove duplicates with enhanced deduplication."""
        import difflib

        # First pass: remove exact duplicates
        seen = set()
        unique_references = []
        for ref in references:
            normalized_ref = ref.strip().lower()
            if normalized_ref not in seen:
                seen.add(normalized_ref)
                unique_references.append(ref)

        # Second pass: remove near-duplicates using similarity threshold
        similarity_threshold = 0.85  # User's preferred threshold
        final_references = []

        for ref in unique_references:
            is_duplicate = False
            for existing_ref in final_references:
                similarity = difflib.SequenceMatcher(None, ref.lower(), existing_ref.lower()).ratio()
                if similarity >= similarity_threshold:
                    is_duplicate = True
                    # Keep the longer reference (likely more complete)
                    if len(ref) > len(existing_ref):
                        final_references[final_references.index(existing_ref)] = ref
                    break

            if not is_duplicate:
                final_references.append(ref)

        # Filter out very short matches and false positives
        filtered_references = [ref for ref in final_references if len(ref.strip()) > 20 and self._is_valid_reference(ref)]

        return filtered_references

    def _extract_reference_metadata(self, ref_text: str) -> Dict[str, Any]:
        """Extract detailed metadata from a reference string."""
        import re

        metadata = {
            'raw_text': ref_text,
            'authors': [],
            'title': '',
            'journal': '',
            'year': None,
            'volume': '',
            'issue': '',
            'pages': '',
            'doi': '',
            'pmid': '',
            'url': '',
            'publisher': '',
            'book_title': '',
            'confidence': 0.0
        }

        # Extract DOI
        doi_match = re.search(r'doi:\s*(10\.\d+/[^\s,;]+)', ref_text, re.IGNORECASE)
        if doi_match:
            metadata['doi'] = doi_match.group(1)
            metadata['confidence'] += 0.2

        # Extract PMID
        pmid_match = re.search(r'pmid:\s*(\d+)', ref_text, re.IGNORECASE)
        if pmid_match:
            metadata['pmid'] = pmid_match.group(1)
            metadata['confidence'] += 0.15

        # Extract URL
        url_match = re.search(r'https?://[^\s,;]+', ref_text, re.IGNORECASE)
        if url_match:
            metadata['url'] = url_match.group(0)
            metadata['confidence'] += 0.1

        # Extract year
        year_match = re.search(r'\b(19|20)\d{2}\b', ref_text)
        if year_match:
            metadata['year'] = int(year_match.group(0))
            metadata['confidence'] += 0.2

        # Extract authors (simplified pattern)
        author_patterns = [
            r'^([A-Z][a-z]+(?:\s+[A-Z]{1,3})?(?:,\s*[A-Z][a-z]+(?:\s+[A-Z]{1,3})?)*)',
            r'([A-Z][a-z]+,\s*[A-Z]\.(?:\s*[A-Z]\.)?(?:,\s*[A-Z][a-z]+,\s*[A-Z]\.(?:\s*[A-Z]\.)?)*)'
        ]

        for pattern in author_patterns:
            author_match = re.search(pattern, ref_text)
            if author_match:
                authors_text = author_match.group(1)
                # Split by comma and clean up
                authors = [author.strip() for author in authors_text.split(',') if author.strip()]
                metadata['authors'] = authors[:5]  # Limit to first 5 authors
                metadata['confidence'] += 0.15
                break

        # Extract title (text between first period and second period, or before journal)
        title_patterns = [
            r'(?:^[^.]+\.\s*)([^.]+)\.\s*([A-Z][^.]*\.\s*\d{4})',  # Author. Title. Journal Year
            r'(?:^[^.]+\.\s*)([^.]+)\.\s*([A-Z][^,]*,\s*\d{4})'   # Author. Title. Publisher, Year
        ]

        for pattern in title_patterns:
            title_match = re.search(pattern, ref_text)
            if title_match:
                metadata['title'] = title_match.group(1).strip()
                metadata['journal'] = title_match.group(2).strip()
                metadata['confidence'] += 0.2
                break

        # Extract volume, issue, and pages
        vol_issue_pages = re.search(r'(\d{4});?\s*(\d+)(?:\((\d+)\))?:(\d+(?:-\d+)?)', ref_text)
        if vol_issue_pages:
            metadata['volume'] = vol_issue_pages.group(2)
            if vol_issue_pages.group(3):
                metadata['issue'] = vol_issue_pages.group(3)
            metadata['pages'] = vol_issue_pages.group(4)
            metadata['confidence'] += 0.15

        # Normalize confidence score
        metadata['confidence'] = min(1.0, metadata['confidence'])

        return metadata
