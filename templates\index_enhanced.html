<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti Knowledge Graph Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
        }
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .score-badge {
            float: right;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
        }
        .vis-network {
            outline: none;
        }
        #dropzone {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        #dropzone.dragover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }
        .file-item {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        #answer-sources {
            max-height: 300px;
            overflow-y: auto;
        }
        .reference-area {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin-top: 10px;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
        .metadata-card {
            margin-bottom: 15px;
        }
        .metadata-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metadata-value {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph Explorer</h1>
        
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="chat-tab" data-bs-toggle="tab" data-bs-target="#chat" type="button" role="tab" aria-controls="chat" aria-selected="true">Chat</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="false">Search</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="entities-tab" data-bs-toggle="tab" data-bs-target="#entities" type="button" role="tab" aria-controls="entities" aria-selected="false">Entities</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="false">Upload</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="metadata-tab" data-bs-toggle="tab" data-bs-target="#metadata" type="button" role="tab" aria-controls="metadata" aria-selected="false">Metadata</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="references-tab" data-bs-toggle="tab" data-bs-target="#references" type="button" role="tab" aria-controls="references" aria-selected="false">References</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="graph-tab" data-bs-toggle="tab" data-bs-target="#graph" type="button" role="tab" aria-controls="graph" aria-selected="false">Graph</button>
            </li>
        </ul>
        
        <!-- Tab Content -->
        <div class="tab-content" id="mainTabsContent">
            <!-- Chat Tab -->
            <div class="tab-pane fade show active" id="chat" role="tabpanel" aria-labelledby="chat-tab">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>Ask a Question</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="question-input" class="form-label">Your Question</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="question-input" placeholder="Ask about the knowledge in your documents...">
                                        <button class="btn btn-primary" type="button" id="question-button">Ask</button>
                                    </div>
                                </div>
                                
                                <div class="loading" id="question-loading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Thinking...</p>
                                </div>
                                
                                <div id="conversation-history" class="mt-4">
                                    <!-- Conversation history will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card" id="answer-sources" style="display: none;">
                            <div class="card-header">
                                <h5>References</h5>
                            </div>
                            <div class="card-body">
                                <div id="sources-list">
                                    <!-- Sources will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search Tab -->
            <div class="tab-pane fade" id="search" role="tabpanel" aria-labelledby="search-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Search Knowledge Graph</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="search-input" class="form-label">Search Query</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search-input" placeholder="Search for information...">
                                <button class="btn btn-primary" type="button" id="search-button">Search</button>
                            </div>
                        </div>
                        
                        <div class="loading" id="search-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Searching...</p>
                        </div>
                        
                        <div id="search-results" class="mt-4">
                            <!-- Search results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Entities Tab -->
            <div class="tab-pane fade" id="entities" role="tabpanel" aria-labelledby="entities-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Explore Entities</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="entity-type-select" class="form-label">Entity Type</label>
                                <select class="form-select" id="entity-type-select">
                                    <option value="all">All Types</option>
                                    <!-- Entity types will be populated dynamically -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="entity-search-input" class="form-label">Search Entities</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="entity-search-input" placeholder="Search for entities...">
                                    <button class="btn btn-primary" type="button" id="entity-search-button">Search</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="loading" id="entity-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading entities...</p>
                        </div>
                        
                        <div id="entity-list" class="mt-4">
                            <!-- Entities will be displayed here -->
                        </div>
                        
                        <div id="entity-details" class="mt-4" style="display: none;">
                            <h4 id="entity-name"></h4>
                            <p id="entity-type" class="badge bg-primary"></p>
                            <p id="entity-description"></p>
                            
                            <h5>Mentions</h5>
                            <div id="entity-mentions">
                                <!-- Entity mentions will be displayed here -->
                            </div>
                            
                            <h5 class="mt-3">Relationships</h5>
                            <div id="entity-relationships">
                                <!-- Entity relationships will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Upload Tab -->
            <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Upload Documents</h5>
                    </div>
                    <div class="card-body">
                        <div id="alert-container">
                            <!-- Alerts will be displayed here -->
                        </div>
                        
                        <div id="dropzone" class="mb-3">
                            <i class="bi bi-cloud-upload fs-1"></i>
                            <h5>Drag & Drop PDF Files Here</h5>
                            <p class="text-muted">or click to select files</p>
                            <input type="file" id="file-input" accept=".pdf" multiple style="display: none;">
                        </div>
                        
                        <div id="file-list" class="mb-3">
                            <!-- File list will be displayed here -->
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                                <input type="number" class="form-control" id="chunk-size" value="1200" min="100" max="10000">
                                <div class="form-text">Recommended: 1200 characters</div>
                            </div>
                            <div class="col-md-6">
                                <label for="overlap" class="form-label">Overlap (characters)</label>
                                <input type="number" class="form-control" id="overlap" value="50" min="0" max="500">
                                <div class="form-text">Recommended: 50 characters</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="extract-metadata" checked>
                                    <label class="form-check-label" for="extract-metadata">
                                        Extract document metadata
                                    </label>
                                </div>
                                <div class="form-text">Extracts title, authors, publication date, etc.</div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="extract-references" checked>
                                    <label class="form-check-label" for="extract-references">
                                        Extract references
                                    </label>
                                </div>
                                <div class="form-text">Extracts bibliographic references from the document</div>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary d-none" id="upload-button">Upload & Process</button>
                        
                        <div id="progress-container" class="mt-3 d-none">
                            <label class="form-label">Upload Progress</label>
                            <div class="progress">
                                <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Metadata Tab -->
            <div class="tab-pane fade" id="metadata" role="tabpanel" aria-labelledby="metadata-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Document Metadata</h5>
                    </div>
                    <div class="card-body">
                        <div id="metadata-alert-container">
                            <!-- Alerts will be displayed here -->
                        </div>
                        
                        <div class="mb-3">
                            <label for="metadata-document-select" class="form-label">Select Document</label>
                            <select class="form-select" id="metadata-document-select">
                                <option value="">Select a document</option>
                                <!-- Documents will be populated dynamically -->
                            </select>
                        </div>
                        
                        <div class="loading" id="metadata-loading" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading metadata...</p>
                        </div>
                        
                        <div id="metadata-content" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card metadata-card">
                                        <div class="card-header">
                                            <h5>Document Information</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="metadata-item">
                                                <div class="metadata-title">Title</div>
                                                <div class="metadata-value" id="metadata-title">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Authors</div>
                                                <div class="metadata-value" id="metadata-authors">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Publication Date</div>
                                                <div class="metadata-value" id="metadata-date">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Journal/Source</div>
                                                <div class="metadata-value" id="metadata-journal">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">DOI</div>
                                                <div class="metadata-value" id="metadata-doi">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Keywords</div>
                                                <div class="metadata-value" id="metadata-keywords">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card metadata-card">
                                        <div class="card-header">
                                            <h5>File Information</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="metadata-item">
                                                <div class="metadata-title">Filename</div>
                                                <div class="metadata-value" id="metadata-filename">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">File Size</div>
                                                <div class="metadata-value" id="metadata-filesize">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Pages</div>
                                                <div class="metadata-value" id="metadata-pages">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Creator</div>
                                                <div class="metadata-value" id="metadata-creator">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Producer</div>
                                                <div class="metadata-value" id="metadata-producer">-</div>
                                            </div>
                                            <div class="metadata-item">
                                                <div class="metadata-title">Creation Date</div>
                                                <div class="metadata-value" id="metadata-creation-date">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card metadata-card mt-3">
                                <div class="card-header">
                                    <h5>Abstract</h5>
                                </div>
                                <div class="card-body">
                                    <div id="metadata-abstract">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- References Tab -->
            <div class="tab-pane fade" id="references" role="tabpanel" aria-labelledby="references-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Document References</h5>
                    </div>
                    <div class="card-body">
                        <div id="references-alert-container">
                            <!-- Alerts will be displayed here -->
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="document-select" class="form-label">Select Document</label>
                                <select class="form-select" id="document-select">
                                    <option value="">Select a document</option>
                                    <!-- Documents will be populated dynamically -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-end h-100">
                                    <div>
                                        <label class="form-label">References</label>
                                        <div><span id="references-count">0</span> references found</div>
                                    </div>
                                    <div>
                                        <button class="btn btn-outline-primary btn-sm" id="download-csv-button" disabled>
                                            <i class="bi bi-download"></i> Download CSV
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" id="download-all-csv-button">
                                            <i class="bi bi-download"></i> Download All CSV
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="reference-search-input" class="form-label">Search References</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="reference-search-input" placeholder="Search in references...">
                                    <button class="btn btn-primary" type="button" id="reference-search-button">Search</button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="reference-filter-select" class="form-label">Filter By</label>
                                <select class="form-select" id="reference-filter-select">
                                    <option value="all">All References</option>
                                    <option value="llm">LLM Extracted</option>
                                    <option value="regex">Regex Extracted</option>
                                </select>
                            </div>
                        </div>
                        
                        <div id="references-content">
                            <div id="references-list">
                                <!-- References will be displayed here -->
                                <div class="alert alert-info">Select a document to view its references.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Graph Tab -->
            <div class="tab-pane fade" id="graph" role="tabpanel" aria-labelledby="graph-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>Knowledge Graph Visualization</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-primary" id="load-graph-button">Load Graph</button>
                            <button class="btn btn-outline-secondary" id="reset-graph-button">Reset View</button>
                        </div>
                        
                        <div id="graph-container" style="height: 600px;">
                            <!-- Graph visualization will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/conversation.js"></script>
    <script src="/static/search.js"></script>
    <script src="/static/entities.js"></script>
    <script src="/static/enhanced_upload.js"></script>
    <script src="/static/metadata.js"></script>
    <script src="/static/references.js"></script>
    <script src="/static/graph.js"></script>
</body>
</html>
