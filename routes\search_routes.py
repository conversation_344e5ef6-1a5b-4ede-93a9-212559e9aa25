"""
Search routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional

from services.search_service import search_knowledge_graph, search_documents, search_facts
from models.knowledge_graph import SearchQuery, SearchResult
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["search"])

@router.post("/search", response_model=SearchResult)
async def search(query: SearchQuery):
    """
    Search the knowledge graph.
    
    Args:
        query: Search query
        
    Returns:
        Search results
    """
    try:
        results = await search_knowledge_graph(query)
        return results
    
    except Exception as e:
        logger.error(f"Error searching knowledge graph: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching knowledge graph: {str(e)}"
        )

@router.get("/search/documents")
async def search_document_api(
    query: str = Query(...),
    limit: int = Query(10, ge=1, le=100)
):
    """
    Search documents.
    
    Args:
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        Search results
    """
    try:
        results = await search_documents(query, limit)
        return {"documents": results, "count": len(results)}
    
    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching documents: {str(e)}"
        )

@router.get("/search/facts")
async def search_facts_api(
    query: str = Query(...),
    limit: int = Query(10, ge=1, le=100)
):
    """
    Search facts.
    
    Args:
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        Search results
    """
    try:
        results = await search_facts(query, limit)
        return {"facts": results, "count": len(results)}
    
    except Exception as e:
        logger.error(f"Error searching facts: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching facts: {str(e)}"
        )
