{% extends "layouts/base.html" %}

{% block title %}Home - Graphiti Knowledge Graph{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active" aria-current="page">Home</li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <div class="row">
        <div class="col-md-12">
            <h1>Welcome to Graphiti Knowledge Graph</h1>
            <p class="lead">Explore your documents through an intelligent knowledge graph</p>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-upload me-2"></i>Upload Documents</h5>
                    <p class="card-text">Upload and process documents to extract entities and relationships.</p>
                    <a href="/batch-upload" class="btn btn-primary">Upload Documents</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-tags me-2"></i>Explore Entities</h5>
                    <p class="card-text">Browse and search entities extracted from your documents.</p>
                    <a href="/entities" class="btn btn-primary">View Entities</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-question-circle me-2"></i>Ask Questions</h5>
                    <p class="card-text">Ask questions about your documents and get AI-powered answers.</p>
                    <a href="/qa" class="btn btn-primary">Q&A Interface</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Recent Documents</h5>
                </div>
                <div class="card-body">
                    <div id="recentDocuments">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/documents" class="btn btn-outline-primary">View All Documents</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Top Entities</h5>
                </div>
                <div class="card-body">
                    <div id="topEntities">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/entities" class="btn btn-outline-primary">View All Entities</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Knowledge Graph Overview</h5>
                </div>
                <div class="card-body">
                    <div id="graphStats" class="row">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="documentCount">-</h3>
                                    <p class="mb-0">Documents</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="entityCount">-</h3>
                                    <p class="mb-0">Entities</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="relationshipCount">-</h3>
                                    <p class="mb-0">Relationships</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3 id="entityTypeCount">-</h3>
                                    <p class="mb-0">Entity Types</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/knowledge-graph" class="btn btn-outline-primary">Explore Knowledge Graph</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Load recent documents
        fetch('/api/documents?limit=5')
            .then(response => response.json())
            .then(data => {
                const recentDocumentsDiv = document.getElementById('recentDocuments');
                if (data.documents && data.documents.length > 0) {
                    const list = document.createElement('ul');
                    list.className = 'list-group';
                    
                    data.documents.forEach(doc => {
                        const item = document.createElement('li');
                        item.className = 'list-group-item d-flex justify-content-between align-items-center';
                        
                        const link = document.createElement('a');
                        link.href = `/document/${doc.uuid}`;
                        link.textContent = doc.title || doc.filename;
                        
                        const badge = document.createElement('span');
                        badge.className = 'badge bg-primary rounded-pill';
                        badge.textContent = new Date(doc.upload_date).toLocaleDateString();
                        
                        item.appendChild(link);
                        item.appendChild(badge);
                        list.appendChild(item);
                    });
                    
                    recentDocumentsDiv.innerHTML = '';
                    recentDocumentsDiv.appendChild(list);
                } else {
                    recentDocumentsDiv.innerHTML = '<div class="alert alert-info">No documents found.</div>';
                }
            })
            .catch(error => {
                console.error('Error loading recent documents:', error);
                document.getElementById('recentDocuments').innerHTML = '<div class="alert alert-danger">Error loading documents.</div>';
            });
        
        // Load top entities
        fetch('/api/entities?limit=5&sort_by=mention_count&sort_order=desc')
            .then(response => response.json())
            .then(data => {
                const topEntitiesDiv = document.getElementById('topEntities');
                if (data.entities && data.entities.length > 0) {
                    const list = document.createElement('ul');
                    list.className = 'list-group';
                    
                    data.entities.forEach(entity => {
                        const item = document.createElement('li');
                        item.className = 'list-group-item d-flex justify-content-between align-items-center';
                        
                        const link = document.createElement('a');
                        link.href = `/entity-detail?uuid=${entity.uuid}`;
                        link.textContent = entity.name;
                        
                        const typeBadge = document.createElement('span');
                        typeBadge.className = 'badge bg-info me-2';
                        typeBadge.textContent = entity.type;
                        
                        const countBadge = document.createElement('span');
                        countBadge.className = 'badge bg-primary rounded-pill';
                        countBadge.textContent = entity.mention_count || 1;
                        
                        item.appendChild(link);
                        item.appendChild(typeBadge);
                        item.appendChild(countBadge);
                        list.appendChild(item);
                    });
                    
                    topEntitiesDiv.innerHTML = '';
                    topEntitiesDiv.appendChild(list);
                } else {
                    topEntitiesDiv.innerHTML = '<div class="alert alert-info">No entities found.</div>';
                }
            })
            .catch(error => {
                console.error('Error loading top entities:', error);
                document.getElementById('topEntities').innerHTML = '<div class="alert alert-danger">Error loading entities.</div>';
            });
        
        // Load graph stats
        fetch('/api/graph-stats')
            .then(response => response.json())
            .then(data => {
                document.getElementById('documentCount').textContent = data.document_count || 0;
                document.getElementById('entityCount').textContent = data.entity_count || 0;
                document.getElementById('relationshipCount').textContent = data.relationship_count || 0;
                document.getElementById('entityTypeCount').textContent = data.entity_type_count || 0;
            })
            .catch(error => {
                console.error('Error loading graph stats:', error);
            });
    });
</script>
{% endblock %}
