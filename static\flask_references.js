/**
 * Enhanced References handling for Graphiti Flask Web Interface
 * 
 * This script provides a comprehensive reference management interface.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced References.js loaded");
    
    // DOM elements
    const referencesTab = document.getElementById('references-tab');
    const referencesLoading = document.getElementById('references-loading');
    const referencesList = document.getElementById('references-list');
    const referenceFilters = document.getElementById('reference-filters');
    const documentSelector = document.getElementById('document-selector');
    const searchInput = document.getElementById('reference-search-input');
    const exportButton = document.getElementById('export-references-button');
    const visualizeButton = document.getElementById('visualize-references-button');
    
    // State
    let allReferences = [];
    let filteredReferences = [];
    let selectedDocument = '';
    let currentFilters = {
        authors: '',
        year: '',
        journal: '',
        extraction_method: ''
    };
    
    // Initialize the references tab
    if (referencesTab) {
        referencesTab.addEventListener('click', function() {
            loadDocumentList();
        });
    }
    
    // Load document list
    async function loadDocumentList() {
        try {
            if (referencesLoading) referencesLoading.style.display = 'block';
            if (referencesList) referencesList.innerHTML = '';
            
            const response = await fetch('/api/references/documents');
            const data = await response.json();
            
            if (response.ok && data.documents) {
                // Populate document selector
                if (documentSelector) {
                    documentSelector.innerHTML = '<option value="">All Documents</option>';
                    data.documents.forEach(doc => {
                        const option = document.createElement('option');
                        option.value = doc;
                        option.textContent = doc;
                        documentSelector.appendChild(option);
                    });
                }
                
                // Load all references
                loadAllReferences();
            } else {
                showError('Error loading documents: ' + (data.detail || 'Unknown error'));
            }
        } catch (error) {
            showError('Error: ' + error.message);
        }
    }
    
    // Load all references
    async function loadAllReferences() {
        try {
            if (referencesLoading) referencesLoading.style.display = 'block';
            
            // Build query parameters
            const params = new URLSearchParams();
            if (selectedDocument) params.append('source_document', selectedDocument);
            if (currentFilters.authors) params.append('authors', currentFilters.authors);
            if (currentFilters.year) params.append('year', currentFilters.year);
            if (currentFilters.journal) params.append('journal', currentFilters.journal);
            if (currentFilters.extraction_method) params.append('extraction_method', currentFilters.extraction_method);
            if (searchInput && searchInput.value) params.append('search_query', searchInput.value);
            
            const response = await fetch(`/api/references?${params.toString()}`);
            const data = await response.json();
            
            if (response.ok) {
                allReferences = data.references || [];
                filteredReferences = [...allReferences];
                
                // Enable export button if references exist
                if (exportButton) exportButton.disabled = allReferences.length === 0;
                if (visualizeButton) visualizeButton.disabled = allReferences.length === 0;
                
                // Display references
                displayReferences(filteredReferences);
            } else {
                showError('Error loading references: ' + (data.detail || 'Unknown error'));
            }
        } catch (error) {
            showError('Error: ' + error.message);
        } finally {
            if (referencesLoading) referencesLoading.style.display = 'none';
        }
    }
    
    // Display references
    function displayReferences(references) {
        if (!referencesList) return;
        
        if (!references || references.length === 0) {
            referencesList.innerHTML = `
                <div class="alert alert-info" role="alert">
                    No references found. Try adjusting your filters or upload a document.
                </div>
            `;
            return;
        }
        
        // Create references container
        const container = document.createElement('div');
        container.className = 'references-container';
        
        // Add reference count
        const countInfo = document.createElement('p');
        countInfo.className = 'text-muted mb-3';
        countInfo.textContent = `Showing ${references.length} references`;
        container.appendChild(countInfo);
        
        // Create references list
        const refsContainer = document.createElement('div');
        refsContainer.className = 'list-group';
        
        // Add each reference
        references.forEach(ref => {
            const refItem = createReferenceItem(ref);
            refsContainer.appendChild(refItem);
        });
        
        container.appendChild(refsContainer);
        referencesList.innerHTML = '';
        referencesList.appendChild(container);
    }
    
    // Create a reference item
    function createReferenceItem(ref) {
        const item = document.createElement('div');
        item.className = 'list-group-item reference-item';
        
        // Create reference header
        const header = document.createElement('div');
        header.className = 'd-flex justify-content-between align-items-start';
        
        // Title
        const title = document.createElement('h5');
        title.className = 'mb-1';
        title.textContent = ref.title || 'Untitled Reference';
        header.appendChild(title);
        
        // Extraction method badge
        const methodBadge = document.createElement('span');
        methodBadge.className = `badge ${ref.extraction_method === 'llm' ? 'bg-primary' : 'bg-secondary'} ms-2`;
        methodBadge.textContent = ref.extraction_method === 'llm' ? 'AI Extracted' : 'Regex Extracted';
        header.appendChild(methodBadge);
        
        item.appendChild(header);
        
        // Authors
        if (ref.authors) {
            const authors = document.createElement('p');
            authors.className = 'mb-1 text-primary';
            authors.textContent = ref.authors;
            item.appendChild(authors);
        }
        
        // Journal, year, volume, issue, pages
        const journalInfo = document.createElement('p');
        journalInfo.className = 'mb-1';
        
        let journalText = '';
        if (ref.journal) journalText += ref.journal;
        if (ref.year) journalText += journalText ? `, ${ref.year}` : ref.year;
        if (ref.volume) journalText += journalText ? `, ${ref.volume}` : ref.volume;
        if (ref.issue) journalText += `(${ref.issue})`;
        if (ref.pages) journalText += journalText ? `, pp. ${ref.pages}` : `pp. ${ref.pages}`;
        
        journalInfo.textContent = journalText || 'No journal information';
        item.appendChild(journalInfo);
        
        // DOI and URL
        if (ref.doi || ref.url) {
            const links = document.createElement('p');
            links.className = 'mb-1';
            
            if (ref.doi) {
                const doiLink = document.createElement('a');
                doiLink.href = ref.doi.startsWith('http') ? ref.doi : `https://doi.org/${ref.doi}`;
                doiLink.target = '_blank';
                doiLink.className = 'me-3';
                doiLink.innerHTML = '<i class="bi bi-link"></i> DOI';
                links.appendChild(doiLink);
            }
            
            if (ref.url) {
                const urlLink = document.createElement('a');
                urlLink.href = ref.url;
                urlLink.target = '_blank';
                urlLink.innerHTML = '<i class="bi bi-link"></i> URL';
                links.appendChild(urlLink);
            }
            
            item.appendChild(links);
        }
        
        // Source document
        const source = document.createElement('small');
        source.className = 'text-muted';
        source.textContent = `Source: ${ref.source_document}`;
        item.appendChild(source);
        
        return item;
    }
    
    // Show error message
    function showError(message) {
        if (!referencesList) return;
        
        referencesList.innerHTML = `
            <div class="alert alert-danger" role="alert">
                ${message}
            </div>
        `;
        if (referencesLoading) referencesLoading.style.display = 'none';
    }
    
    // Visualize references
    function visualizeReferences(references) {
        if (!referencesList) return;
        
        // Create a container for the visualization
        const visContainer = document.createElement('div');
        visContainer.id = 'reference-visualization';
        visContainer.className = 'mb-4';
        visContainer.style.height = '400px';
        
        // Add the container to the page
        referencesList.innerHTML = '';
        referencesList.appendChild(visContainer);
        
        // Prepare data for visualization
        const nodes = [];
        const edges = [];
        
        // Add document nodes
        const documents = new Set();
        references.forEach(ref => {
            if (ref.source_document) {
                documents.add(ref.source_document);
            }
        });
        
        documents.forEach(doc => {
            nodes.push({
                id: 'doc_' + doc,
                label: doc,
                group: 'document',
                shape: 'box',
                color: {
                    background: '#4CAF50',
                    border: '#388E3C',
                    highlight: {
                        background: '#81C784',
                        border: '#388E3C'
                    }
                }
            });
        });
        
        // Add reference nodes
        references.forEach((ref, index) => {
            const label = ref.title || 'Untitled Reference';
            nodes.push({
                id: 'ref_' + index,
                label: label.length > 30 ? label.substring(0, 27) + '...' : label,
                title: `${ref.authors ? ref.authors + ': ' : ''}${ref.title || 'Untitled'}`,
                group: 'reference',
                shape: 'ellipse',
                color: {
                    background: '#2196F3',
                    border: '#1565C0',
                    highlight: {
                        background: '#64B5F6',
                        border: '#1565C0'
                    }
                }
            });
            
            // Connect reference to document
            if (ref.source_document) {
                edges.push({
                    from: 'doc_' + ref.source_document,
                    to: 'ref_' + index,
                    arrows: 'to',
                    color: {
                        color: '#757575',
                        highlight: '#424242'
                    }
                });
            }
        });
        
        // Create the network
        const data = {
            nodes: new vis.DataSet(nodes),
            edges: new vis.DataSet(edges)
        };
        
        const options = {
            physics: {
                stabilization: true,
                barnesHut: {
                    gravitationalConstant: -2000,
                    centralGravity: 0.3,
                    springLength: 150,
                    springConstant: 0.04,
                    damping: 0.09
                }
            },
            nodes: {
                font: {
                    size: 12
                }
            },
            edges: {
                smooth: {
                    type: 'continuous'
                }
            },
            interaction: {
                navigationButtons: true,
                keyboard: true
            }
        };
        
        // Initialize the network
        const network = new vis.Network(visContainer, data, options);
        
        // Add visualization controls
        const controls = document.createElement('div');
        controls.className = 'btn-group mt-2';
        
        const resetBtn = document.createElement('button');
        resetBtn.className = 'btn btn-outline-secondary btn-sm';
        resetBtn.textContent = 'Reset View';
        resetBtn.addEventListener('click', () => network.fit());
        
        const listViewBtn = document.createElement('button');
        listViewBtn.className = 'btn btn-outline-secondary btn-sm';
        listViewBtn.textContent = 'List View';
        listViewBtn.addEventListener('click', () => displayReferences(references));
        
        controls.appendChild(resetBtn);
        controls.appendChild(listViewBtn);
        
        referencesList.appendChild(controls);
    }
    
    // Event listeners for filters
    if (documentSelector) {
        documentSelector.addEventListener('change', function() {
            selectedDocument = this.value;
            loadAllReferences();
        });
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            loadAllReferences();
        }, 500));
    }
    
    if (exportButton) {
        exportButton.addEventListener('click', function() {
            window.location.href = '/api/references/csv';
        });
    }
    
    if (visualizeButton) {
        visualizeButton.addEventListener('click', function() {
            visualizeReferences(filteredReferences);
        });
    }
    
    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
});
