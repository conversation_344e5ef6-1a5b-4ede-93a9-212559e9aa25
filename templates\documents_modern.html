{% extends "base_modern.html" %}

{% block title %}Documents - Graphiti{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Documents</h1>
        <p class="text-muted">Manage your knowledge base documents</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="window.location.href='/enhanced-upload'">
            <i class="bi bi-plus-circle"></i> Upload Documents
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="refreshDocuments()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card gradient-card stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-documents">-</div>
                <div class="stats-label">
                    <i class="bi bi-file-earmark-text"></i> Total Documents
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-success stats-card">
            <div class="card-body">
                <div class="stats-number" id="processed-documents">-</div>
                <div class="stats-label">
                    <i class="bi bi-check-circle"></i> Processed
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-warning stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-chunks">-</div>
                <div class="stats-label">
                    <i class="bi bi-puzzle"></i> Text Chunks
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-info stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-size">-</div>
                <div class="stats-label">
                    <i class="bi bi-hdd"></i> Total Size
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="bi bi-funnel"></i> Search & Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-input" placeholder="Search documents...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="sort-by">
                    <option value="processed_at">Date Processed</option>
                    <option value="name">Name</option>
                    <option value="file_size">File Size</option>
                    <option value="chunks_count">Chunks</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="sort-order">
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="file-type-filter">
                    <option value="">All Types</option>
                    <option value="pdf">PDF</option>
                    <option value="word">Word</option>
                    <option value="excel">Excel</option>
                    <option value="powerpoint">PowerPoint</option>
                    <option value="text">Text</option>
                    <option value="html">HTML</option>
                    <option value="image">Image</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="bi bi-funnel"></i> Apply
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Documents Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="bi bi-files"></i> Document Library</h5>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="toggleView('table')">
                <i class="bi bi-table"></i> Table
            </button>
            <button class="btn btn-sm btn-outline-primary ms-1" onclick="toggleView('grid')">
                <i class="bi bi-grid-3x3"></i> Grid
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Loading State -->
        <div id="documents-loading" class="text-center py-5">
            <div class="loading-spinner"></div>
            <p class="text-muted mt-2">Loading documents...</p>
        </div>
        
        <!-- Table View -->
        <div id="table-view" style="display: none;">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Document</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Chunks</th>
                            <th>Processed</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="documents-table-body">
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Grid View -->
        <div id="grid-view" style="display: none;">
            <div class="row p-3" id="documents-grid">
            </div>
        </div>
        
        <!-- Empty State -->
        <div id="empty-state" class="text-center py-5" style="display: none;">
            <i class="bi bi-file-earmark-x fs-1 text-muted mb-3"></i>
            <h5>No Documents Found</h5>
            <p class="text-muted">Upload your first document to get started</p>
            <button class="btn btn-primary" onclick="window.location.href='/enhanced-upload'">
                <i class="bi bi-plus-circle"></i> Upload Documents
            </button>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <small class="text-muted">
                    Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> 
                    of <span id="total-count">0</span> documents
                </small>
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Document Details Modal -->
<div class="modal fade" id="document-details-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Document Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="document-details-content">
                <div class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Loading document details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="download-document-btn">
                    <i class="bi bi-download"></i> Download
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="delete-confirmation-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this document? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will also delete all associated chunks, entities, and embeddings.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn">
                    <i class="bi bi-trash"></i> Delete Document
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentLimit = 20;
let currentView = 'table';
let documents = [];
let selectedDocumentId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadDocuments();
    setupEventListeners();
});

function setupEventListeners() {
    // Search input with debounce
    let searchTimeout;
    document.getElementById('search-input').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1;
            loadDocuments();
        }, 500);
    });
    
    // Sort change handlers
    document.getElementById('sort-by').addEventListener('change', () => {
        currentPage = 1;
        loadDocuments();
    });
    
    document.getElementById('sort-order').addEventListener('change', () => {
        currentPage = 1;
        loadDocuments();
    });
}

async function loadDocuments() {
    try {
        showLoading();
        
        const searchTerm = document.getElementById('search-input').value;
        const sortBy = document.getElementById('sort-by').value;
        const sortOrder = document.getElementById('sort-order').value;
        const fileType = document.getElementById('file-type-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            limit: currentLimit,
            sort_by: sortBy,
            sort_order: sortOrder
        });
        
        if (searchTerm) params.append('search', searchTerm);
        if (fileType) params.append('file_type', fileType);
        
        const response = await fetch(`/api/documents?${params}`);
        if (!response.ok) throw new Error('Failed to load documents');
        
        const data = await response.json();
        documents = data.documents || [];
        
        updateStats(data);
        displayDocuments(documents);
        updatePagination(data.pagination || {});
        
    } catch (error) {
        console.error('Error loading documents:', error);
        showAlert('Error loading documents: ' + error.message, 'danger');
        showEmptyState();
    }
}

function updateStats(data) {
    document.getElementById('total-documents').textContent = data.total_count || 0;
    document.getElementById('processed-documents').textContent = documents.filter(d => d.status === 'processed').length;
    document.getElementById('total-chunks').textContent = documents.reduce((sum, d) => sum + (d.chunks_count || 0), 0);
    
    const totalSize = documents.reduce((sum, d) => sum + (d.file_size || 0), 0);
    document.getElementById('total-size').textContent = formatFileSize(totalSize);
}

function displayDocuments(docs) {
    hideLoading();
    
    if (!docs || docs.length === 0) {
        showEmptyState();
        return;
    }
    
    if (currentView === 'table') {
        displayTableView(docs);
    } else {
        displayGridView(docs);
    }
}

function displayTableView(docs) {
    const tbody = document.getElementById('documents-table-body');
    tbody.innerHTML = '';
    
    docs.forEach(doc => {
        const row = document.createElement('tr');
        row.className = 'fade-in';
        row.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        ${getFileTypeIcon(doc.file_extension || '')}
                    </div>
                    <div>
                        <h6 class="mb-0">${doc.filename || doc.name || 'Untitled'}</h6>
                        <small class="text-muted">${doc.file_path || ''}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-primary">${getFileType(doc.file_extension || '')}</span>
            </td>
            <td>${formatFileSize(doc.file_size || 0)}</td>
            <td>
                <span class="badge bg-info">${doc.chunks_count || 0}</span>
            </td>
            <td>
                <small class="text-muted">${doc.upload_date ? formatDate(doc.upload_date) : (doc.processed_at ? formatDate(doc.processed_at) : 'Unknown')}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewDocument('${doc.uuid}')">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="downloadDocument('${doc.uuid}')">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteDocument('${doc.uuid}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    document.getElementById('table-view').style.display = 'block';
    document.getElementById('grid-view').style.display = 'none';
}

function displayGridView(docs) {
    const grid = document.getElementById('documents-grid');
    grid.innerHTML = '';
    
    docs.forEach(doc => {
        const card = document.createElement('div');
        card.className = 'col-md-4 col-lg-3 mb-3';
        card.innerHTML = `
            <div class="card h-100 fade-in">
                <div class="card-body">
                    <div class="text-center mb-3">
                        ${getFileTypeIcon(doc.file_extension || '', 'fs-1')}
                    </div>
                    <h6 class="card-title text-center">${doc.name || 'Untitled'}</h6>
                    <div class="text-center mb-3">
                        <span class="badge bg-primary">${getFileType(doc.file_extension || '')}</span>
                        <span class="badge bg-info ms-1">${doc.chunks_count || 0} chunks</span>
                    </div>
                    <div class="text-center">
                        <small class="text-muted">${formatFileSize(doc.file_size || 0)}</small>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewDocument('${doc.uuid}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="downloadDocument('${doc.uuid}')">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteDocument('${doc.uuid}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        grid.appendChild(card);
    });
    
    document.getElementById('grid-view').style.display = 'block';
    document.getElementById('table-view').style.display = 'none';
}

function getFileTypeIcon(extension, className = '') {
    const iconMap = {
        '.pdf': `<i class="bi bi-file-earmark-pdf text-danger ${className}"></i>`,
        '.doc': `<i class="bi bi-file-earmark-word text-primary ${className}"></i>`,
        '.docx': `<i class="bi bi-file-earmark-word text-primary ${className}"></i>`,
        '.xls': `<i class="bi bi-file-earmark-excel text-success ${className}"></i>`,
        '.xlsx': `<i class="bi bi-file-earmark-excel text-success ${className}"></i>`,
        '.ppt': `<i class="bi bi-file-earmark-ppt text-warning ${className}"></i>`,
        '.pptx': `<i class="bi bi-file-earmark-ppt text-warning ${className}"></i>`,
        '.txt': `<i class="bi bi-file-earmark-text text-secondary ${className}"></i>`,
        '.html': `<i class="bi bi-file-earmark-code text-info ${className}"></i>`,
        '.jpg': `<i class="bi bi-file-earmark-image text-purple ${className}"></i>`,
        '.png': `<i class="bi bi-file-earmark-image text-purple ${className}"></i>`
    };
    
    return iconMap[extension.toLowerCase()] || `<i class="bi bi-file-earmark text-secondary ${className}"></i>`;
}

function getFileType(extension) {
    const typeMap = {
        '.pdf': 'PDF',
        '.doc': 'Word',
        '.docx': 'Word',
        '.xls': 'Excel',
        '.xlsx': 'Excel',
        '.ppt': 'PowerPoint',
        '.pptx': 'PowerPoint',
        '.txt': 'Text',
        '.html': 'HTML',
        '.jpg': 'Image',
        '.png': 'Image'
    };
    
    return typeMap[extension.toLowerCase()] || 'Document';
}

async function viewDocument(documentId) {
    try {
        selectedDocumentId = documentId;
        const modal = new bootstrap.Modal(document.getElementById('document-details-modal'));
        modal.show();
        
        const response = await fetch(`/api/documents/${documentId}`);
        if (!response.ok) throw new Error('Failed to load document details');
        
        const doc = await response.json();
        displayDocumentDetails(doc);
        
    } catch (error) {
        console.error('Error viewing document:', error);
        showAlert('Error loading document details: ' + error.message, 'danger');
    }
}

function displayDocumentDetails(doc) {
    const content = document.getElementById('document-details-content');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${doc.filename || doc.name || 'Unknown'}</td></tr>
                    <tr><td><strong>Type:</strong></td><td>${getFileType(doc.file_extension || '')}</td></tr>
                    <tr><td><strong>Size:</strong></td><td>${formatFileSize(doc.file_size || 0)}</td></tr>
                    <tr><td><strong>Chunks:</strong></td><td>${doc.chunks_count || 0}</td></tr>
                    <tr><td><strong>Processed:</strong></td><td>${doc.upload_date ? formatDate(doc.upload_date) : (doc.processed_at ? formatDate(doc.processed_at) : 'Unknown')}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Processing Details</h6>
                <table class="table table-sm">
                    <tr><td><strong>Status:</strong></td><td><span class="badge bg-success">${doc.status || 'Unknown'}</span></td></tr>
                    <tr><td><strong>Entities:</strong></td><td>${doc.entities_count || 0}</td></tr>
                    <tr><td><strong>References:</strong></td><td>${doc.references_count || 0}</td></tr>
                    <tr><td><strong>Embeddings:</strong></td><td>${doc.embeddings_count || 0}</td></tr>
                </table>
            </div>
        </div>
        ${doc.metadata ? `
            <div class="mt-3">
                <h6>Metadata</h6>
                <pre class="bg-light p-3 rounded">${JSON.stringify(doc.metadata, null, 2)}</pre>
            </div>
        ` : ''}
    `;
}

async function downloadDocument(documentId) {
    try {
        const response = await fetch(`/api/documents/${documentId}/download`);
        if (!response.ok) throw new Error('Failed to download document');
        
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `document-${documentId}`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        showAlert('Document downloaded successfully', 'success', 2000);
        
    } catch (error) {
        console.error('Error downloading document:', error);
        showAlert('Error downloading document: ' + error.message, 'danger');
    }
}

function deleteDocument(documentId) {
    selectedDocumentId = documentId;
    const modal = new bootstrap.Modal(document.getElementById('delete-confirmation-modal'));
    modal.show();
    
    document.getElementById('confirm-delete-btn').onclick = async function() {
        try {
            const response = await fetch(`/api/documents/${documentId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) throw new Error('Failed to delete document');
            
            modal.hide();
            showAlert('Document deleted successfully', 'success', 2000);
            loadDocuments(); // Refresh the list
            
        } catch (error) {
            console.error('Error deleting document:', error);
            showAlert('Error deleting document: ' + error.message, 'danger');
        }
    };
}

function toggleView(view) {
    currentView = view;
    displayDocuments(documents);
}

function applyFilters() {
    currentPage = 1;
    loadDocuments();
}

function refreshDocuments() {
    const button = event.target;
    const hideLoading = showLoading(button);
    
    loadDocuments().finally(() => {
        hideLoading();
        showAlert('Documents refreshed successfully', 'success', 2000);
    });
}

function updatePagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    const totalPages = pagination.total_pages || 1;
    
    // Update showing info
    document.getElementById('showing-start').textContent = ((currentPage - 1) * currentLimit) + 1;
    document.getElementById('showing-end').textContent = Math.min(currentPage * currentLimit, pagination.total_count || 0);
    document.getElementById('total-count').textContent = pagination.total_count || 0;
    
    // Generate pagination
    paginationEl.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
    paginationEl.appendChild(prevLi);
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        paginationEl.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
    paginationEl.appendChild(nextLi);
}

function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadDocuments();
}

function showLoading() {
    document.getElementById('documents-loading').style.display = 'block';
    document.getElementById('table-view').style.display = 'none';
    document.getElementById('grid-view').style.display = 'none';
    document.getElementById('empty-state').style.display = 'none';
}

function hideLoading() {
    document.getElementById('documents-loading').style.display = 'none';
}

function showEmptyState() {
    hideLoading();
    document.getElementById('empty-state').style.display = 'block';
    document.getElementById('table-view').style.display = 'none';
    document.getElementById('grid-view').style.display = 'none';
}
</script>
{% endblock %}
