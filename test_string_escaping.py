#!/usr/bin/env python3
"""
Test script to verify the improved string escaping functionality.
"""

from database.falkordb_adapter import <PERSON>alkorDBAdapter

def test_string_escaping():
    """Test the improved string escaping."""
    adapter = FalkorDBAdapter()

    # Test problematic strings that were causing errors
    test_strings = [
        "the patient's story is paramount",
        "individual's mosaic and across",
        "e-Monitor'No. 31, May 2010",
        "2 to 18 weeks' duration",
        "text with; semicolon",
        "text with `backticks`",
        'text with "double quotes"',
        "text with \\backslashes\\",
        "text with\nnewlines\nand\ttabs",
        "text with special chars: @#$%^&*()",
        "very long text " * 1000  # Test truncation
    ]

    print('Testing improved string escaping:')
    print('=' * 50)
    
    for i, test_str in enumerate(test_strings):
        escaped = adapter._escape_cypher_string(test_str)
        print(f'{i+1}. Original: {test_str[:50]}{"..." if len(test_str) > 50 else ""}')
        print(f'   Escaped:  {escaped[:50]}{"..." if len(escaped) > 50 else ""}')
        print(f'   Length:   {len(test_str)} -> {len(escaped)}')
        print()

if __name__ == "__main__":
    test_string_escaping()
