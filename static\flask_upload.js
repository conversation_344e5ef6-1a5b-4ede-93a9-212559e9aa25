/**
 * Enhanced Upload handling for Graphiti Flask Web Interface
 *
 * This script handles the upload functionality in the Upload tab with support for multiple file types.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced Upload.js loaded");

    // Constants
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const MAX_FILE_SIZE_DISPLAY = formatFileSize(MAX_FILE_SIZE);

    // Supported file types
    const SUPPORTED_FILE_TYPES = {
        // Document formats
        'application/pdf': { icon: 'bi-file-earmark-pdf', label: 'PDF' },
        'text/plain': { icon: 'bi-file-earmark-text', label: 'Text' },
        'text/markdown': { icon: 'bi-file-earmark-text', label: 'Markdown' },
        'application/rtf': { icon: 'bi-file-earmark-richtext', label: 'RTF' },
        'application/msword': { icon: 'bi-file-earmark-word', label: 'Word' },
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: 'bi-file-earmark-word', label: 'Word' },
        'application/vnd.oasis.opendocument.text': { icon: 'bi-file-earmark-text', label: 'ODT' },

        // Web formats
        'text/html': { icon: 'bi-file-earmark-code', label: 'HTML' },
        'application/xml': { icon: 'bi-file-earmark-code', label: 'XML' },

        // Spreadsheet formats
        'text/csv': { icon: 'bi-file-earmark-spreadsheet', label: 'CSV' },
        'application/vnd.ms-excel': { icon: 'bi-file-earmark-excel', label: 'Excel' },
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { icon: 'bi-file-earmark-excel', label: 'Excel' },

        // Presentation formats
        'application/vnd.ms-powerpoint': { icon: 'bi-file-earmark-slides', label: 'PowerPoint' },
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': { icon: 'bi-file-earmark-slides', label: 'PowerPoint' },

        // E-book formats
        'application/epub+zip': { icon: 'bi-book', label: 'EPUB' }
    };

    // Get DOM elements
    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('file-input');
    const folderInput = document.getElementById('folder-input');
    const selectFileButton = document.getElementById('select-file-button');
    const selectFolderButton = document.getElementById('select-folder-button');
    const selectedFiles = document.getElementById('selected-files');
    const uploadButton = document.getElementById('upload-button');
    const uploadProgress = document.getElementById('upload-progress');
    const uploadStatus = document.getElementById('upload-status');
    const processingProgress = document.getElementById('processing-progress');
    const processingStatus = document.getElementById('processing-status');
    const processingStep = document.getElementById('processing-step');
    const uploadResults = document.getElementById('upload-results');
    const uploadResultsContent = document.getElementById('upload-results-content');
    const maxFileSizeInfo = document.getElementById('max-file-size-info');
    const supportedFormatsInfo = document.getElementById('supported-formats-info');

    // Progress tracking variables
    let currentDocumentId = null;
    let progressInterval = null;

    // Update the max file size info
    if (maxFileSizeInfo) {
        maxFileSizeInfo.textContent = `Maximum file size: ${MAX_FILE_SIZE_DISPLAY}`;
    }

    // Update the supported formats info
    if (supportedFormatsInfo) {
        const formatsList = Object.entries(SUPPORTED_FILE_TYPES).map(([mimeType, info]) => info.label);
        const uniqueFormats = [...new Set(formatsList)];
        supportedFormatsInfo.textContent = `Supported formats: ${uniqueFormats.join(', ')}`;
    }

    // Add event listener for the select file button
    if (selectFileButton) {
        selectFileButton.addEventListener('click', function() {
            fileInput.click();
        });
    }

    // Add event listener for the select folder button
    if (selectFolderButton) {
        selectFolderButton.addEventListener('click', function() {
            folderInput.click();
        });
    }

    // Add event listener for file input change
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
    }

    // Add event listener for folder input change
    if (folderInput) {
        folderInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
    }

    // Add event listeners for drag and drop
    if (dropzone) {
        dropzone.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.add('dragover');
        });

        dropzone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');
        });

        dropzone.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');

            const dt = e.dataTransfer;

            // Check if items are available (for folder support)
            if (dt.items && dt.items.length > 0) {
                // Use DataTransferItemList interface to access the files
                handleDropItems(dt.items);
            } else {
                // Use DataTransfer interface to access the files
                handleFiles(dt.files);
            }
        });

        /**
         * Handle dropped items (supports folders)
         * @param {DataTransferItemList} items - Dropped items
         */
        function handleDropItems(items) {
            const allFiles = [];
            let pendingFolders = 0;

            // Function to process all entries
            function processEntries() {
                if (pendingFolders === 0 && allFiles.length > 0) {
                    // Convert FileList to Array
                    handleFiles(allFiles);
                }
            }

            // Process each item
            for (let i = 0; i < items.length; i++) {
                const item = items[i];

                // Skip non-file entries
                if (item.kind !== 'file') {
                    continue;
                }

                // Get the entry
                const entry = item.webkitGetAsEntry ? item.webkitGetAsEntry() : item.getAsEntry();

                if (entry) {
                    if (entry.isFile) {
                        // Get file directly
                        const file = item.getAsFile();
                        if (file) {
                            allFiles.push(file);
                        }
                    } else if (entry.isDirectory) {
                        // Process directory
                        pendingFolders++;
                        processDirectory(entry, allFiles, () => {
                            pendingFolders--;
                            processEntries();
                        });
                    }
                } else {
                    // Fallback for browsers that don't support webkitGetAsEntry
                    const file = item.getAsFile();
                    if (file) {
                        allFiles.push(file);
                    }
                }
            }

            // If no folders to process, handle files immediately
            if (pendingFolders === 0) {
                processEntries();
            }
        }

        /**
         * Process a directory recursively
         * @param {DirectoryEntry} directoryEntry - The directory entry
         * @param {Array} files - Array to collect files
         * @param {Function} callback - Callback when done
         */
        function processDirectory(directoryEntry, files, callback) {
            const dirReader = directoryEntry.createReader();
            let entriesChunk = [];

            // Read directory entries in chunks
            function readEntries() {
                dirReader.readEntries((entries) => {
                    if (entries.length === 0) {
                        // No more entries, process the collected entries
                        processEntryChunk(entriesChunk, files, callback);
                    } else {
                        // Add entries to the chunk and continue reading
                        entriesChunk = entriesChunk.concat(Array.from(entries));
                        readEntries();
                    }
                }, (error) => {
                    console.error('Error reading directory:', error);
                    callback();
                });
            }

            // Start reading entries
            readEntries();
        }

        /**
         * Process a chunk of directory entries
         * @param {Array} entries - Directory entries
         * @param {Array} files - Array to collect files
         * @param {Function} callback - Callback when done
         */
        function processEntryChunk(entries, files, callback) {
            let pendingEntries = entries.length;

            if (pendingEntries === 0) {
                callback();
                return;
            }

            // Process each entry
            entries.forEach((entry) => {
                if (entry.isFile) {
                    // Get file
                    entry.file((file) => {
                        files.push(file);
                        pendingEntries--;
                        if (pendingEntries === 0) {
                            callback();
                        }
                    }, (error) => {
                        console.error('Error getting file:', error);
                        pendingEntries--;
                        if (pendingEntries === 0) {
                            callback();
                        }
                    });
                } else if (entry.isDirectory) {
                    // Process subdirectory
                    processDirectory(entry, files, () => {
                        pendingEntries--;
                        if (pendingEntries === 0) {
                            callback();
                        }
                    });
                } else {
                    pendingEntries--;
                    if (pendingEntries === 0) {
                        callback();
                    }
                }
            });
        }
    }

    // Add event listener for the upload button
    if (uploadButton) {
        uploadButton.addEventListener('click', function() {
            uploadFiles();
        });
    }

    /**
     * Handle selected files
     *
     * @param {FileList} files - The selected files
     */
    function handleFiles(files) {
        // Clear the selected files
        if (selectedFiles) {
            selectedFiles.innerHTML = '';
        }

        // Check if any files were selected
        if (files.length === 0) {
            if (uploadButton) {
                uploadButton.disabled = true;
            }
            return;
        }

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Check if the file type is supported
            let fileTypeInfo = SUPPORTED_FILE_TYPES[file.type];

            // If the MIME type isn't recognized, try to determine from the file extension
            if (!fileTypeInfo) {
                const fileName = file.name.toLowerCase();
                if (fileName.endsWith('.pdf')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-pdf', label: 'PDF' };
                } else if (fileName.endsWith('.txt')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-text', label: 'Text' };
                } else if (fileName.endsWith('.md')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-text', label: 'Markdown' };
                } else if (fileName.endsWith('.rtf')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-richtext', label: 'RTF' };
                } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-word', label: 'Word' };
                } else if (fileName.endsWith('.odt')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-text', label: 'ODT' };
                } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-code', label: 'HTML' };
                } else if (fileName.endsWith('.xml')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-code', label: 'XML' };
                } else if (fileName.endsWith('.csv')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-spreadsheet', label: 'CSV' };
                } else if (fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-excel', label: 'Excel' };
                } else if (fileName.endsWith('.ppt') || fileName.endsWith('.pptx')) {
                    fileTypeInfo = { icon: 'bi-file-earmark-slides', label: 'PowerPoint' };
                } else if (fileName.endsWith('.epub')) {
                    fileTypeInfo = { icon: 'bi-book', label: 'EPUB' };
                }
            }

            if (!fileTypeInfo) {
                if (selectedFiles) {
                    selectedFiles.innerHTML += `
                        <div class="alert alert-danger">
                            ${file.name} is not a supported file type. Please upload one of the supported formats.
                        </div>
                    `;
                }
                continue;
            }

            // Check if the file size is within limits
            if (file.size > MAX_FILE_SIZE) {
                if (selectedFiles) {
                    selectedFiles.innerHTML += `
                        <div class="alert alert-danger">
                            ${file.name} exceeds the maximum file size of ${MAX_FILE_SIZE_DISPLAY}.
                        </div>
                    `;
                }
                continue;
            }

            // Create a file item
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi ${fileTypeInfo.icon}"></i>
                        ${file.name} (${formatFileSize(file.size)})
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;

            // Add event listener for the remove button
            const removeButton = fileItem.querySelector('.remove-file');
            removeButton.addEventListener('click', function() {
                fileItem.remove();

                // Disable the upload button if no files are selected
                if (selectedFiles && selectedFiles.children.length === 0) {
                    if (uploadButton) {
                        uploadButton.disabled = true;
                    }
                }
            });

            // Add the file item to the selected files
            if (selectedFiles) {
                selectedFiles.appendChild(fileItem);
            }
        }

        // Enable the upload button
        if (uploadButton) {
            uploadButton.disabled = false;
        }
    }

    /**
     * Upload the selected files
     */
    function uploadFiles() {
        // Get the selected files
        if (fileInput.files.length === 0) {
            alert('Please select at least one file to upload.');
            return;
        }

        // Get the form data
        const formData = new FormData();

        // If multiple files are selected, use the batch upload endpoint
        const isMultipleFiles = fileInput.files.length > 1;

        // Add all selected files to the form data
        if (isMultipleFiles) {
            // Add each file to the form data with the 'files' key
            for (let i = 0; i < fileInput.files.length; i++) {
                formData.append('files', fileInput.files[i]);
            }
        } else {
            // Single file upload - use the regular 'file' key
            formData.append('file', fileInput.files[0]);
        }

        // Get the chunk size and overlap
        const chunkSize = document.getElementById('chunk-size')?.value || 1200;
        const overlap = document.getElementById('overlap')?.value || 0;

        // Get the extract entities and references checkboxes
        const extractEntities = document.getElementById('extract-entities')?.checked || true;
        const extractReferences = document.getElementById('extract-references')?.checked || true;

        // Add the form data
        formData.append('chunk_size', chunkSize);
        formData.append('overlap', overlap);
        formData.append('extract_entities', extractEntities);
        formData.append('extract_references', extractReferences);

        // For batch uploads, add the max_parallel_processes parameter
        if (isMultipleFiles) {
            formData.append('max_parallel_processes', 4); // Default to 4 parallel processes
        }

        // Show the upload progress
        if (uploadProgress) {
            uploadProgress.style.display = 'block';
        }
        if (uploadResults) {
            uploadResults.style.display = 'none';
        }

        // Update the progress bar
        const progressBar = uploadProgress?.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
        }

        // Update the status
        if (uploadStatus) {
            uploadStatus.textContent = 'Uploading file...';
        }

        // Upload the file(s) - use enhanced endpoints for better progress tracking
        const uploadEndpoint = isMultipleFiles ? '/api/enhanced/batch-enhanced-upload' : '/api/enhanced/enhanced-upload';

        // Update the status text based on number of files
        if (uploadStatus) {
            uploadStatus.textContent = isMultipleFiles
                ? `Uploading ${fileInput.files.length} files...`
                : 'Uploading file...';
        }

        // Upload the file(s)
        fetch(uploadEndpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide the upload progress
            if (uploadProgress) {
                uploadProgress.style.display = 'none';
            }

            // Check if we're handling a batch upload or single upload
            const isBatchResponse = Array.isArray(data) || data.results !== undefined;

            // Check if the upload was successful
            if (data.status === 'processing' || data.success) {
                if (isBatchResponse) {
                    // Handle batch upload response
                    handleBatchUploadResponse(data);
                } else {
                    // Handle single file upload response
                    handleSingleFileUploadResponse(data);
                }

                // Clear the file input
                fileInput.value = '';
                if (selectedFiles) {
                    selectedFiles.innerHTML = '';
                }
                if (uploadButton) {
                    uploadButton.disabled = true;
                }
            } else {
                // Show the upload results for error
                if (uploadResults) {
                    uploadResults.style.display = 'block';
                }

                // Display the error
                if (uploadResultsContent) {
                    uploadResultsContent.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>Error</h5>
                            <p>${data.error || 'An error occurred during processing.'}</p>
                            <p class="mt-2">If you're seeing OCR-related errors, the system will automatically try alternative methods to process your document.</p>
                        </div>
                    `;
                }
            }
        })

    /**
     * Handle the response from a single file upload
     *
     * @param {Object} data - The response data from the API
     */
    function handleSingleFileUploadResponse(data) {
        // Enhanced upload always runs background processing
        if (data.status === 'processing' && data.operation_id) {
            // Show processing progress
            if (processingProgress) {
                processingProgress.style.display = 'block';
            }

            // Set the current operation ID for progress tracking
            currentDocumentId = data.operation_id;

            // Start tracking progress
            startProgressTracking(currentDocumentId);

            // Show initial status
            if (processingStatus) {
                processingStatus.textContent = 'Processing document...';
            }
            if (processingStep) {
                processingStep.textContent = 'Starting document processing';
            }

            // Update progress bar
            const progressBar = processingProgress?.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = '5%';
                progressBar.setAttribute('aria-valuenow', 5);
            }
        } else {
            // Show the upload results immediately
            if (uploadResults) {
                uploadResults.style.display = 'block';
            }

            // Display the results
            if (uploadResultsContent) {
                uploadResultsContent.innerHTML = `
                    <div class="alert alert-success">
                        <h5>Success!</h5>
                        <p>${data.message || 'File uploaded and processed successfully.'}</p>
                    </div>
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6>File Information</h6>
                            <p><strong>Filename:</strong> ${data.filename}</p>
                            <p><strong>File Type:</strong> ${data.file_type || 'Document'}</p>
                            <p><strong>Chunks Processed:</strong> ${data.chunks}</p>
                            <p><strong>Entities Extracted:</strong> ${data.entities}</p>
                            <p><strong>References Extracted:</strong> ${data.references}</p>
                            <p><strong>Embeddings Generated:</strong> ${data.embeddings_generated || 0}</p>
                            <p><strong>Embedding Model:</strong> ${data.embedding_model || 'none'}</p>
                        </div>
                    </div>
                `;
            }
        }
    }

    /**
     * Handle the response from a batch upload
     *
     * @param {Object} data - The response data from the API
     */
    function handleBatchUploadResponse(data) {
        // Show the upload results immediately
        if (uploadResults) {
            uploadResults.style.display = 'block';
        }

        // Handle enhanced batch upload response format
        const results = Array.isArray(data) ? data : (data.results || []);
        const successCount = results.filter(r => r.status === 'processing').length;
        const totalCount = results.length;

        // Create the HTML for the results
        let resultsHTML = `
            <div class="alert alert-${successCount === totalCount ? 'success' : 'warning'}">
                <h5>${successCount === totalCount ? 'Success!' : 'Partial Success'}</h5>
                <p>Started processing ${successCount} of ${totalCount} files successfully.</p>
            </div>
        `;

        // Add a table of documents
        if (results && results.length > 0) {
            resultsHTML += `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Status</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add a row for each document
            results.forEach(doc => {
                resultsHTML += `
                    <tr>
                        <td>${doc.filename}</td>
                        <td>
                            <span class="badge bg-${doc.status === 'processing' ? 'success' : 'danger'}">
                                ${doc.status === 'processing' ? 'Processing' : 'Failed'}
                            </span>
                        </td>
                        <td>
                            ${doc.status === 'processing'
                                ? `Operation ID: ${doc.operation_id}<br>Processing in background...`
                                : `Error: ${doc.error || 'Unknown error'}`}
                        </td>
                    </tr>
                `;
            });

            resultsHTML += `
                        </tbody>
                    </table>
                </div>
                <div class="alert alert-info mt-3">
                    <p><i class="bi bi-info-circle"></i> Documents are being processed in the background. You can check the status in the Documents tab.</p>
                </div>
            `;
        }

        // Display the results
        if (uploadResultsContent) {
            uploadResultsContent.innerHTML = resultsHTML;
        }
    }
})
.catch(error => {
    console.error('Error:', error);

    // Hide the upload progress
    if (uploadProgress) {
        uploadProgress.style.display = 'none';
    }

    // Hide the processing progress
    if (processingProgress) {
        processingProgress.style.display = 'none';
    }

    // Stop progress tracking if it's running
    stopProgressTracking();

    // Show the upload results
    if (uploadResults) {
        uploadResults.style.display = 'block';
    }

    // Display the error
    if (uploadResultsContent) {
        uploadResultsContent.innerHTML = `
            <div class="alert alert-danger">
                <h5>Error</h5>
                <p>${error.message}</p>
                <p class="mt-2">If you're seeing OCR-related errors, the system will automatically try alternative methods to process your document.</p>
            </div>
        `;
    }
});
    }

    /**
     * Start tracking the progress of document processing
     *
     * @param {string} operationId - The operation ID of the document being processed
     */
    function startProgressTracking(operationId) {
        // Clear any existing interval
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // Get the filename from the file input
        const filename = fileInput.files[0]?.name || "Unknown file";

        // Initialize enhanced progress tracking
        if (typeof initEnhancedProgressTracking === 'function') {
            initEnhancedProgressTracking(operationId, filename);
        } else {
            // Fallback to basic progress tracking
            progressInterval = setInterval(() => {
                checkDocumentProgress(operationId);
            }, 2000); // Check every 2 seconds
        }
    }

    /**
     * Check the progress of document processing
     *
     * @param {string} operationId - The operation ID of the document being processed
     */
    function checkDocumentProgress(operationId) {
        fetch(`/api/enhanced/progress/${operationId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Update the progress UI
                updateProgressUI(data);

                // If processing is complete or failed, stop tracking
                if (data.status === 'completed' || data.status === 'failed') {
                    stopProgressTracking();

                    // If completed, show the final results
                    if (data.status === 'completed') {
                        showProcessingResults(data);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking document progress:', error);
            });
    }

    /**
     * Update the progress UI with the latest progress data
     *
     * @param {Object} progressData - The progress data from the API
     */
    function updateProgressUI(progressData) {
        // Update progress bar
        const progressBar = processingProgress?.querySelector('.progress-bar');
        if (progressBar) {
            const percentage = progressData.progress_percentage || 0;
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        // Update status text
        if (processingStatus) {
            if (progressData.status === 'processing') {
                processingStatus.textContent = 'Processing document...';
            } else if (progressData.status === 'completed') {
                processingStatus.textContent = 'Processing complete!';
            } else if (progressData.status === 'failed') {
                processingStatus.textContent = 'Processing failed';
            } else if (progressData.status === 'queued') {
                processingStatus.textContent = 'Waiting in queue...';
            }
        }

        // Update step text
        if (processingStep) {
            processingStep.textContent = progressData.current_step_name || progressData.step_name || 'Processing...';
        }
    }

    /**
     * Stop tracking the progress of document processing
     */
    function stopProgressTracking() {
        if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
        }

        // Stop enhanced progress tracking if available
        if (typeof stopEnhancedProgressTracking === 'function') {
            stopEnhancedProgressTracking();
        }
    }

    /**
     * Show the final processing results
     *
     * @param {Object} progressData - The progress data from the API
     */
    function showProcessingResults(progressData) {
        // Hide the processing progress
        if (processingProgress) {
            processingProgress.style.display = 'none';
        }

        // Show the upload results
        if (uploadResults) {
            uploadResults.style.display = 'block';
        }

        // Get the details from the progress data
        const details = progressData.details || {};

        // Display the results
        if (uploadResultsContent) {
            uploadResultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h5>Success!</h5>
                    <p>File uploaded and processed successfully.</p>
                </div>
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>File Information</h6>
                        <p><strong>Filename:</strong> ${progressData.document_name || progressData.filename || 'Unknown'}</p>
                        <p><strong>File Type:</strong> document</p>
                        <p><strong>Facts Extracted:</strong> ${details.facts_count || 0}</p>
                        <p><strong>Entities Extracted:</strong> ${details.entities_count || 0}</p>
                        <p><strong>References Extracted:</strong> ${details.references_count || 0}</p>
                        <p><strong>Embeddings Generated:</strong> ${details.embeddings_count || 0}</p>
                        <p><strong>Processing Time:</strong> ${Math.round(details.processing_time || 0)}s</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Format file size in a human-readable format
     *
     * @param {number} bytes - The file size in bytes
     * @returns {string} The formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
