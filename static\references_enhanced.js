/**
 * Enhanced references display for Graphiti Knowledge Graph
 * 
 * This script provides improved reference display in the UI, including:
 * 1. Better formatting of reference metadata
 * 2. Support for different citation styles
 * 3. Improved filtering and search
 * 4. Enhanced visualization
 */

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Reference elements
    const referencesList = document.getElementById('references-list');
    const referencesLoading = document.getElementById('references-loading');
    const searchInput = document.getElementById('reference-search');
    const filterForm = document.getElementById('reference-filter-form');
    const exportButton = document.getElementById('export-references-button');
    const visualizeButton = document.getElementById('visualize-references-button');
    const citationStyleSelect = document.getElementById('citation-style-select');
    
    // Reference data
    let allReferences = [];
    let filteredReferences = [];
    let currentCitationStyle = 'apa'; // Default citation style
    
    // Initialize citation style selector if it exists
    if (citationStyleSelect) {
        citationStyleSelect.addEventListener('change', function() {
            currentCitationStyle = this.value;
            displayReferences(filteredReferences);
        });
    } else {
        // Create citation style selector if it doesn't exist
        createCitationStyleSelector();
    }
    
    // Initialize search and filters
    if (searchInput) {
        searchInput.addEventListener('input', filterReferences);
    }
    
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            loadReferences();
        });
    }
    
    // Initialize export button
    if (exportButton) {
        exportButton.addEventListener('click', exportReferences);
    }
    
    // Initialize visualize button
    if (visualizeButton) {
        visualizeButton.addEventListener('click', function() {
            visualizeReferences(filteredReferences);
        });
    }
    
    // Load references on page load
    loadReferences();
    
    /**
     * Create citation style selector if it doesn't exist
     */
    function createCitationStyleSelector() {
        // Check if we have a container for the selector
        const container = document.querySelector('.reference-controls');
        if (!container) return;
        
        // Create the selector
        const selectorDiv = document.createElement('div');
        selectorDiv.className = 'form-group me-2';
        
        const label = document.createElement('label');
        label.htmlFor = 'citation-style-select';
        label.className = 'form-label';
        label.textContent = 'Citation Style:';
        
        const select = document.createElement('select');
        select.id = 'citation-style-select';
        select.className = 'form-select form-select-sm';
        
        // Add options
        const options = [
            { value: 'apa', text: 'APA' },
            { value: 'mla', text: 'MLA' },
            { value: 'chicago', text: 'Chicago' }
        ];
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
        
        // Add event listener
        select.addEventListener('change', function() {
            currentCitationStyle = this.value;
            displayReferences(filteredReferences);
        });
        
        // Add to container
        selectorDiv.appendChild(label);
        selectorDiv.appendChild(select);
        container.appendChild(selectorDiv);
        
        // Store the selector
        citationStyleSelect = select;
    }
    
    /**
     * Load references from the API
     */
    async function loadReferences() {
        if (!referencesList) return;
        
        try {
            // Show loading indicator
            if (referencesLoading) referencesLoading.style.display = 'flex';
            
            // Build query parameters
            const params = new URLSearchParams();
            
            if (filterForm) {
                // Add filter parameters
                const sourceDocument = filterForm.querySelector('#filter-document')?.value;
                const authors = filterForm.querySelector('#filter-authors')?.value;
                const year = filterForm.querySelector('#filter-year')?.value;
                const journal = filterForm.querySelector('#filter-journal')?.value;
                const extractionMethod = filterForm.querySelector('#filter-method')?.value;
                const searchQuery = searchInput?.value;
                
                if (sourceDocument) params.append('source_document', sourceDocument);
                if (authors) params.append('authors', authors);
                if (year) params.append('year', year);
                if (journal) params.append('journal', journal);
                if (extractionMethod) params.append('extraction_method', extractionMethod);
                if (searchQuery) params.append('search_query', searchQuery);
            }
            
            const response = await fetch(`/api/references?${params.toString()}`);
            const data = await response.json();
            
            if (response.ok) {
                allReferences = data.references || [];
                filteredReferences = [...allReferences];
                
                // Enable export button if references exist
                if (exportButton) exportButton.disabled = allReferences.length === 0;
                if (visualizeButton) visualizeButton.disabled = allReferences.length === 0;
                
                // Display references
                displayReferences(filteredReferences);
                
                // Update statistics if available
                updateReferenceStatistics(data.statistics);
            } else {
                showError('Error loading references: ' + (data.detail || 'Unknown error'));
            }
        } catch (error) {
            showError('Error: ' + error.message);
        } finally {
            if (referencesLoading) referencesLoading.style.display = 'none';
        }
    }
    
    /**
     * Filter references based on search input
     */
    function filterReferences() {
        if (!searchInput || !allReferences) return;
        
        const searchTerm = searchInput.value.toLowerCase();
        
        if (!searchTerm) {
            filteredReferences = [...allReferences];
        } else {
            filteredReferences = allReferences.filter(ref => {
                return (
                    (ref.title && ref.title.toLowerCase().includes(searchTerm)) ||
                    (ref.authors && ref.authors.toLowerCase().includes(searchTerm)) ||
                    (ref.journal && ref.journal.toLowerCase().includes(searchTerm)) ||
                    (ref.year && ref.year.toString().includes(searchTerm)) ||
                    (ref.reference_text && ref.reference_text.toLowerCase().includes(searchTerm))
                );
            });
        }
        
        displayReferences(filteredReferences);
    }
    
    /**
     * Display references in the UI
     * 
     * @param {Array} references - Array of reference objects
     */
    function displayReferences(references) {
        if (!referencesList) return;
        
        // Create references container
        const container = document.createElement('div');
        container.className = 'references-container';
        
        // Add reference count
        const countInfo = document.createElement('p');
        countInfo.className = 'text-muted mb-3';
        countInfo.textContent = `Showing ${references.length} references`;
        container.appendChild(countInfo);
        
        // Create references list
        const refsContainer = document.createElement('div');
        refsContainer.className = 'list-group';
        
        // Add each reference
        references.forEach(ref => {
            const refItem = createReferenceItem(ref);
            refsContainer.appendChild(refItem);
        });
        
        container.appendChild(refsContainer);
        referencesList.innerHTML = '';
        referencesList.appendChild(container);
    }
    
    /**
     * Create a reference item
     * 
     * @param {Object} ref - Reference object
     * @returns {HTMLElement} Reference item element
     */
    function createReferenceItem(ref) {
        const refItem = document.createElement('div');
        refItem.className = 'list-group-item';
        
        // Get citation based on selected style
        let citation = '';
        if (currentCitationStyle === 'apa' && ref.citation_apa) {
            citation = ref.citation_apa;
        } else if (currentCitationStyle === 'mla' && ref.citation_mla) {
            citation = ref.citation_mla;
        } else if (currentCitationStyle === 'chicago' && ref.citation_chicago) {
            citation = ref.citation_chicago;
        } else {
            // Fallback to constructing citation from components
            citation = constructCitation(ref);
        }
        
        // Create citation element
        const citationElement = document.createElement('p');
        citationElement.className = 'mb-1';
        citationElement.innerHTML = citation;
        
        // Create metadata section
        const metadataSection = document.createElement('div');
        metadataSection.className = 'reference-metadata small text-muted mt-2';
        
        // Add metadata fields
        const metadataFields = [];
        
        if (ref.doi) {
            metadataFields.push(`<strong>DOI:</strong> <a href="https://doi.org/${ref.doi}" target="_blank">${ref.doi}</a>`);
        }
        
        if (ref.pmid) {
            metadataFields.push(`<strong>PMID:</strong> <a href="https://pubmed.ncbi.nlm.nih.gov/${ref.pmid}" target="_blank">${ref.pmid}</a>`);
        }
        
        if (ref.source_document) {
            metadataFields.push(`<strong>Source:</strong> ${ref.source_document}`);
        }
        
        if (ref.extraction_method) {
            const method = ref.extraction_method.charAt(0).toUpperCase() + ref.extraction_method.slice(1);
            metadataFields.push(`<strong>Extraction:</strong> ${method}`);
        }
        
        metadataSection.innerHTML = metadataFields.join(' | ');
        
        // Add elements to item
        refItem.appendChild(citationElement);
        refItem.appendChild(metadataSection);
        
        return refItem;
    }
    
    /**
     * Construct a citation from reference components
     * 
     * @param {Object} ref - Reference object
     * @returns {string} Formatted citation
     */
    function constructCitation(ref) {
        let citation = '';
        
        // Authors
        if (ref.authors) {
            citation += `${ref.authors}`;
        }
        
        // Year
        if (ref.year) {
            citation += ` (${ref.year}).`;
        }
        
        // Title
        if (ref.title) {
            citation += ` ${ref.title}.`;
        }
        
        // Journal
        if (ref.journal) {
            citation += ` <em>${ref.journal}</em>`;
        }
        
        // Volume and issue
        if (ref.volume) {
            citation += `, ${ref.volume}`;
            if (ref.issue) {
                citation += `(${ref.issue})`;
            }
        }
        
        // Pages
        if (ref.pages) {
            citation += `, ${ref.pages}`;
        }
        
        // DOI
        if (ref.doi) {
            citation += `. https://doi.org/${ref.doi}`;
        }
        
        return citation || ref.reference_text || 'Unknown reference';
    }
    
    /**
     * Update reference statistics in the UI
     * 
     * @param {Object} statistics - Reference statistics
     */
    function updateReferenceStatistics(statistics) {
        if (!statistics) return;
        
        // Update count elements if they exist
        const referencesCount = document.getElementById('references-count');
        const documentsCount = document.getElementById('documents-count');
        const journalsCount = document.getElementById('journals-count');
        const authorsCount = document.getElementById('authors-count');
        
        if (referencesCount) referencesCount.textContent = statistics.total_references || 0;
        if (documentsCount) documentsCount.textContent = statistics.total_documents || 0;
        if (journalsCount) journalsCount.textContent = statistics.total_journals || 0;
        if (authorsCount) authorsCount.textContent = statistics.total_authors || 0;
    }
    
    /**
     * Export references to CSV
     */
    function exportReferences() {
        if (filteredReferences.length === 0) return;
        
        // Build query parameters for export
        const params = new URLSearchParams();
        params.append('format', 'csv');
        
        // Add filter parameters if available
        if (filterForm) {
            const sourceDocument = filterForm.querySelector('#filter-document')?.value;
            const authors = filterForm.querySelector('#filter-authors')?.value;
            const year = filterForm.querySelector('#filter-year')?.value;
            const journal = filterForm.querySelector('#filter-journal')?.value;
            const extractionMethod = filterForm.querySelector('#filter-method')?.value;
            const searchQuery = searchInput?.value;
            
            if (sourceDocument) params.append('source_document', sourceDocument);
            if (authors) params.append('authors', authors);
            if (year) params.append('year', year);
            if (journal) params.append('journal', journal);
            if (extractionMethod) params.append('extraction_method', extractionMethod);
            if (searchQuery) params.append('search_query', searchQuery);
        }
        
        // Open export URL in new window
        window.open(`/api/references/export?${params.toString()}`, '_blank');
    }
    
    /**
     * Show error message
     * 
     * @param {string} message - Error message
     */
    function showError(message) {
        if (!referencesList) return;
        
        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger';
        errorAlert.textContent = message;
        
        referencesList.innerHTML = '';
        referencesList.appendChild(errorAlert);
    }
});
