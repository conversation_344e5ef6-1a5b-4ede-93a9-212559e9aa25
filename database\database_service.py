"""
Database service for the Graphiti application.
"""

import asyncio
from typing import List, Dict, Any, Optional, Union, Tuple

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.config import FALKORDB_GRAPH
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Global adapter instance
_adapter = None

async def get_falkordb_adapter() -> GraphitiFalkorDBAdapter:
    """
    Get a FalkorDB adapter instance.
    
    Returns:
        FalkorDB adapter instance
    """
    global _adapter
    
    if _adapter is None or not _adapter.is_connected():
        _adapter = GraphitiFalkorDBAdapter(FALKORDB_GRAPH)
    
    return _adapter

async def execute_cypher(query: str, params: Dict[str, Any] = None) -> List[Any]:
    """
    Execute a Cypher query.
    
    Args:
        query: Cypher query
        params: Query parameters
        
    Returns:
        Query results
    """
    adapter = await get_falkordb_adapter()
    return adapter.execute_cypher(query, params)

async def create_episode_node(name: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create an Episode node.
    
    Args:
        name: Name of the episode
        properties: Additional properties
        
    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()
    
    # Prepare properties
    props = {"name": name}
    if properties:
        props.update(properties)
    
    # Ensure UUID is generated
    if "uuid" not in props:
        import uuid
        props["uuid"] = str(uuid.uuid4())
    
    # Create node
    return adapter.create_node("Episode", props)

async def create_fact_node(body: str, episode_uuid: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create a Fact node and link it to an Episode.
    
    Args:
        body: Text content of the fact
        episode_uuid: UUID of the episode
        properties: Additional properties
        
    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()
    
    # Prepare properties
    props = {"body": body}
    if properties:
        props.update(properties)
    
    # Ensure UUID is generated
    if "uuid" not in props:
        import uuid
        props["uuid"] = str(uuid.uuid4())
    
    # Create node
    fact_uuid = adapter.create_node("Fact", props)
    
    if fact_uuid:
        # Create relationship
        adapter.create_relationship(episode_uuid, fact_uuid, "CONTAINS")
    
    return fact_uuid

async def create_entity_node(name: str, entity_type: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create an Entity node.
    
    Args:
        name: Name of the entity
        entity_type: Type of the entity
        properties: Additional properties
        
    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()
    
    # Prepare properties
    props = {"name": name, "type": entity_type}
    if properties:
        props.update(properties)
    
    # Ensure UUID is generated
    if "uuid" not in props:
        import uuid
        props["uuid"] = str(uuid.uuid4())
    
    # Create node
    return adapter.create_node("Entity", props)

async def link_entity_to_fact(entity_uuid: str, fact_uuid: str, properties: Dict[str, Any] = None) -> bool:
    """
    Link an Entity to a Fact.
    
    Args:
        entity_uuid: UUID of the entity
        fact_uuid: UUID of the fact
        properties: Relationship properties
        
    Returns:
        True if the relationship was created, False otherwise
    """
    adapter = await get_falkordb_adapter()
    
    # Create relationship
    return adapter.create_relationship(fact_uuid, entity_uuid, "MENTIONS", properties)

async def create_relationship(from_uuid: str, to_uuid: str, rel_type: str, properties: Dict[str, Any] = None) -> bool:
    """
    Create a relationship between two nodes.
    
    Args:
        from_uuid: UUID of the source node
        to_uuid: UUID of the target node
        rel_type: Relationship type
        properties: Relationship properties
        
    Returns:
        True if the relationship was created, False otherwise
    """
    adapter = await get_falkordb_adapter()
    
    # Create relationship
    return adapter.create_relationship(from_uuid, to_uuid, rel_type, properties)

async def get_entities_by_type(entity_type: str, limit: int = 100) -> List[Dict[str, Any]]:
    """
    Get entities by type.
    
    Args:
        entity_type: Type of the entity
        limit: Maximum number of entities to return
        
    Returns:
        List of entity properties
    """
    query = f"""
    MATCH (e:Entity)
    WHERE e.type = '{entity_type}'
    RETURN e
    LIMIT {limit}
    """
    
    result = await execute_cypher(query)
    
    if result and len(result) > 1:
        return [row[0] for row in result[1]]
    
    return []

async def get_entity_types() -> List[str]:
    """
    Get all entity types.
    
    Returns:
        List of entity types
    """
    query = """
    MATCH (e:Entity)
    RETURN DISTINCT e.type
    """
    
    result = await execute_cypher(query)
    
    if result and len(result) > 1:
        return [row[0] for row in result[1]]
    
    return []

async def get_entity_counts() -> Dict[str, int]:
    """
    Get counts of entities by type.
    
    Returns:
        Dictionary mapping entity types to counts
    """
    query = """
    MATCH (e:Entity)
    RETURN e.type, count(e) as count
    """
    
    result = await execute_cypher(query)
    
    if result and len(result) > 1:
        return {row[0]: row[1] for row in result[1]}
    
    return {}
