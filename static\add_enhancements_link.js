/**
 * Add a link to the enhancements page
 */
document.addEventListener('DOMContentLoaded', function() {
    // Create the button
    const enhancementsButton = document.createElement('a');
    enhancementsButton.href = '/static/enhancements.html';
    enhancementsButton.className = 'btn btn-primary mb-3';
    enhancementsButton.target = '_blank';
    enhancementsButton.innerHTML = '<i class="bi bi-tools"></i> Reference Enhancements';
    enhancementsButton.style.marginRight = '10px';
    
    // Find the container to add the button to
    const container = document.querySelector('.container');
    if (container) {
        // Find the h1 element
        const h1 = container.querySelector('h1');
        if (h1) {
            // Insert the button after the h1
            h1.insertAdjacentElement('afterend', enhancementsButton);
        } else {
            // If no h1, insert at the beginning of the container
            container.insertAdjacentElement('afterbegin', enhancementsButton);
        }
    }
});
