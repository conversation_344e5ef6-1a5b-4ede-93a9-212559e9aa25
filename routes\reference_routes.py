"""
Reference-related routes for the Graphiti application.
"""

import os
from fastapi import APIRouter, HTTPException, Query, Depends, BackgroundTasks, File, UploadFile
from fastapi.responses import FileResponse, JSONResponse
from typing import List, Dict, Any, Optional
from pathlib import Path

from services.reference_service import (
    get_all_references, extract_references_from_document,
    get_reference_statistics, build_citation_network,
    get_document_references
)
from models.reference import ReferenceList, ReferenceFilter, ExtractionMethod, CitationNetwork
from utils.config import REFERENCES_DIR
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["references"])

@router.get("/references", response_model=ReferenceList)
async def get_references(
    source_document: Optional[str] = None,
    authors: Optional[str] = None,
    year: Optional[str] = None,
    journal: Optional[str] = None,
    extraction_method: Optional[ExtractionMethod] = None,
    search_query: Optional[str] = None
):
    """
    Get all references.

    Args:
        source_document: Filter by source document
        authors: Filter by authors
        year: Filter by year
        journal: Filter by journal
        extraction_method: Filter by extraction method
        search_query: Search query

    Returns:
        List of references
    """
    try:
        # Create filter
        filter = ReferenceFilter(
            source_document=source_document,
            authors=authors,
            year=year,
            journal=journal,
            extraction_method=extraction_method,
            search_query=search_query
        )

        # Get references
        references = await get_all_references(filter)
        return references

    except Exception as e:
        logger.error(f"Error getting references: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting references: {str(e)}"
        )

@router.get("/references/csv")
async def get_references_csv():
    """
    Download all references as a CSV file.

    Returns:
        CSV file
    """
    try:
        # Path to the master references CSV file
        master_csv_path = REFERENCES_DIR / "all_references.csv"

        # Check if the file exists
        if not os.path.exists(master_csv_path):
            raise HTTPException(
                status_code=404,
                detail="No references found"
            )

        # Return the file
        return FileResponse(
            path=str(master_csv_path),
            filename="all_references.csv",
            media_type="text/csv"
        )

    except Exception as e:
        logger.error(f"Error getting references CSV: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting references CSV: {str(e)}"
        )

@router.post("/references/extract")
async def extract_references(
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = None
):
    """
    Extract references from a document.

    Args:
        file: Document file
        background_tasks: Background tasks

    Returns:
        Extraction result
    """
    try:
        # Save the file
        import uuid
        from utils.file_utils import save_uploaded_file
        from utils.config import UPLOADS_DIR

        file_content = await file.read()
        file_path = save_uploaded_file(file_content, file.filename, UPLOADS_DIR)

        # Extract references
        if background_tasks:
            # Add reference extraction as a background task
            background_tasks.add_task(
                extract_references_from_document,
                file_path,
                "openai"
            )

            return {
                "message": "Reference extraction started in the background",
                "file_path": str(file_path)
            }
        else:
            # Extract references synchronously
            result = await extract_references_from_document(file_path, "openai")
            return result

    except Exception as e:
        logger.error(f"Error extracting references: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting references: {str(e)}"
        )

@router.get("/references/documents")
async def get_reference_documents():
    """
    Get a list of documents with references.

    Returns:
        List of documents
    """
    try:
        # Get all references
        references = await get_all_references()

        # Extract unique document names
        documents = set()
        for ref in references.references:
            if ref.source_document:
                documents.add(ref.source_document)

        return {"documents": sorted(list(documents))}

    except Exception as e:
        logger.error(f"Error getting reference documents: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting reference documents: {str(e)}"
        )

@router.get("/references/statistics")
async def get_reference_stats():
    """
    Get statistics about references.

    Returns:
        Reference statistics
    """
    try:
        stats = await get_reference_statistics()
        return stats

    except Exception as e:
        logger.error(f"Error getting reference statistics: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting reference statistics: {str(e)}"
        )

@router.get("/references/network")
async def get_citation_network():
    """
    Get the citation network.

    Returns:
        Citation network
    """
    try:
        network = await build_citation_network()
        return network

    except Exception as e:
        logger.error(f"Error getting citation network: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting citation network: {str(e)}"
        )

@router.get("/document-references/{document_name}")
async def get_references_for_document(document_name: str):
    """
    Get references for a specific document.

    Args:
        document_name: Document name

    Returns:
        References for the document
    """
    try:
        result = await get_document_references(document_name)
        return result

    except Exception as e:
        logger.error(f"Error getting document references: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document references: {str(e)}"
        )
