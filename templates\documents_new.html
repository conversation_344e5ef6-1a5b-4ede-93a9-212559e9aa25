<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documents - Graphiti Knowledge Graph</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
    <style>
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph Documents</h1>

        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle active" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item active" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Documents</li>
            </ol>
        </nav>

        <!-- Documents List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Document List</h5>
                <a href="/batch-upload" class="btn btn-primary">
                    <i class="bi bi-upload"></i> Upload Documents
                </a>
            </div>
            <div class="card-body">
                <div id="documents-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading documents...</p>
                </div>
                <div id="documents-list">
                    <!-- Documents will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load documents when the page loads
            loadDocuments();
        });

        // Load documents from the API
        function loadDocuments() {
            // Show loading spinner
            const loadingSpinner = document.getElementById('documents-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'flex';
            }
            
            // Fetch documents
            fetch('/api/documents?page=1&page_size=10')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Render documents
                    renderDocuments(data);
                    
                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading documents:', error);
                    
                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                    }
                    
                    // Show error message
                    const documentsList = document.getElementById('documents-list');
                    if (documentsList) {
                        documentsList.innerHTML = `<div class="alert alert-danger">Error loading documents: ${error.message}</div>`;
                    }
                });
        }

        // Render documents in the UI
        function renderDocuments(data) {
            const documentsList = document.getElementById('documents-list');
            if (!documentsList) {
                console.error("Documents list element not found");
                return;
            }
            
            // Clear existing content
            documentsList.innerHTML = '';
            
            // If no documents, show message
            if (!data.documents || data.documents.length === 0) {
                documentsList.innerHTML = '<div class="alert alert-info">No documents found.</div>';
                return;
            }
            
            // Create documents table
            const table = document.createElement('table');
            table.className = 'table table-striped table-hover';
            
            // Create table header
            const thead = document.createElement('thead');
            thead.innerHTML = `
                <tr>
                    <th>Filename</th>
                    <th>Type</th>
                    <th>Upload Date</th>
                    <th>Chunks</th>
                    <th>Entities</th>
                    <th>References</th>
                    <th>Actions</th>
                </tr>
            `;
            table.appendChild(thead);
            
            // Create table body
            const tbody = document.createElement('tbody');
            
            // Add document rows
            data.documents.forEach(doc => {
                const row = document.createElement('tr');
                
                // Format date
                let formattedDate = 'Unknown';
                try {
                    const uploadDate = new Date(doc.upload_date);
                    formattedDate = uploadDate.toLocaleDateString() + ' ' + uploadDate.toLocaleTimeString();
                } catch (e) {
                    console.error(`Error formatting date for document ${doc.uuid}:`, e);
                }
                
                row.innerHTML = `
                    <td>${doc.filename || 'Unknown'}</td>
                    <td>${doc.file_type || 'Unknown'}</td>
                    <td>${formattedDate}</td>
                    <td>${doc.chunks || 0}</td>
                    <td>${doc.entities || 0}</td>
                    <td>${doc.references || 0}</td>
                    <td>
                        <a href="/documents/${doc.uuid}" class="btn btn-sm btn-primary">View</a>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
            
            table.appendChild(tbody);
            documentsList.appendChild(table);
            
            // Add pagination if available
            if (data.total > data.page_size) {
                const paginationContainer = document.createElement('div');
                paginationContainer.className = 'mt-4 d-flex justify-content-center';
                
                const pagination = document.createElement('nav');
                pagination.setAttribute('aria-label', 'Document pagination');
                
                const paginationList = document.createElement('ul');
                paginationList.className = 'pagination';
                
                // Calculate pagination values
                const totalPages = Math.ceil(data.total / data.page_size);
                const currentPage = data.page;
                const hasPrev = currentPage > 1;
                const hasNext = currentPage < totalPages;
                
                // Previous page button
                const prevItem = document.createElement('li');
                prevItem.className = `page-item ${hasPrev ? '' : 'disabled'}`;
                
                const prevLink = document.createElement('a');
                prevLink.className = 'page-link';
                prevLink.href = '#';
                prevLink.textContent = 'Previous';
                prevLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (hasPrev) {
                        loadDocumentPage(currentPage - 1);
                    }
                });
                
                prevItem.appendChild(prevLink);
                paginationList.appendChild(prevItem);
                
                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    const pageItem = document.createElement('li');
                    pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
                    
                    const pageLink = document.createElement('a');
                    pageLink.className = 'page-link';
                    pageLink.href = '#';
                    pageLink.textContent = i;
                    pageLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        loadDocumentPage(i);
                    });
                    
                    pageItem.appendChild(pageLink);
                    paginationList.appendChild(pageItem);
                }
                
                // Next page button
                const nextItem = document.createElement('li');
                nextItem.className = `page-item ${hasNext ? '' : 'disabled'}`;
                
                const nextLink = document.createElement('a');
                nextLink.className = 'page-link';
                nextLink.href = '#';
                nextLink.textContent = 'Next';
                nextLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (hasNext) {
                        loadDocumentPage(currentPage + 1);
                    }
                });
                
                nextItem.appendChild(nextLink);
                paginationList.appendChild(nextItem);
                
                pagination.appendChild(paginationList);
                paginationContainer.appendChild(pagination);
                documentsList.appendChild(paginationContainer);
            }
        }

        // Load a specific page of documents
        function loadDocumentPage(page) {
            // Show loading spinner
            const loadingSpinner = document.getElementById('documents-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'flex';
            }
            
            // Fetch documents
            fetch(`/api/documents?page=${page}&page_size=10`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Render documents
                    renderDocuments(data);
                    
                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading document page:', error);
                    
                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                    }
                    
                    // Show error message
                    const documentsList = document.getElementById('documents-list');
                    if (documentsList) {
                        documentsList.innerHTML = `<div class="alert alert-danger">Error loading documents: ${error.message}</div>`;
                    }
                });
        }
    </script>
</body>
</html>
