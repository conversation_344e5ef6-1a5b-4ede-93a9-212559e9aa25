"""
Question answering service for the Graphiti application.
"""

import os
import json
import re
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter

# Set up logger
logger = get_logger(__name__)

async def get_relevant_facts(question: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get facts relevant to a question.

    Args:
        question: Question
        limit: Maximum number of facts to return

    Returns:
        Relevant facts
    """
    adapter = await get_falkordb_adapter()

    # Extract keywords from the question
    keywords = extract_keywords(question)

    # Build the query to get facts with document metadata from Episode nodes
    cypher_query = """
    MATCH (f:Fact)
    WHERE
    """

    # Add keyword conditions
    keyword_conditions = []
    for keyword in keywords:
        # Escape single quotes in the keyword
        escaped_keyword = keyword.replace("'", "\\'")
        keyword_conditions.append(f"f.body CONTAINS '{escaped_keyword}'")

    cypher_query += " AND ".join(keyword_conditions)

    # Add the Episode match after the WHERE clause
    cypher_query += """
    WITH f
    MATCH (e:Episode)-[:CONTAINS]->(f)
    RETURN
        f.uuid as uuid,
        f.body as body,
        f.chunk_num as chunk_num,
        e.uuid as document_id,
        e.title as document_title,
        e.name as document_name,
        '' as document_author,
        '' as document_year
    """

    # Add limit
    cypher_query += f"LIMIT {limit}"

    # Execute the query
    result = adapter.execute_cypher(cypher_query)

    # Process the results
    facts = []

    if result and len(result) > 1:
        for row in result[1]:
            uuid = row[0]
            body = row[1]
            chunk_num = row[2]
            document_id = row[3] if len(row) > 3 else None
            document_title = row[4] if len(row) > 4 else "Unknown document"
            document_name = row[5] if len(row) > 5 else document_title
            document_author = row[6] if len(row) > 6 else "Unknown author"
            document_year = row[7] if len(row) > 7 else ""

            facts.append({
                "uuid": uuid,
                "body": body,
                "chunk_num": chunk_num,
                "document_id": document_id,
                "document_title": document_title,
                "document_name": document_name,
                "document_author": document_author,
                "document_year": document_year
            })

    return facts

def extract_keywords(text: str) -> List[str]:
    """
    Extract keywords from text.

    Args:
        text: Text to extract keywords from

    Returns:
        List of keywords
    """
    # Remove punctuation and convert to lowercase
    text = re.sub(r'[^\w\s]', ' ', text.lower())

    # Split into words
    words = text.split()

    # Remove stop words
    stop_words = {
        "a", "an", "the", "and", "or", "but", "if", "because", "as", "what",
        "when", "where", "which", "who", "whom", "whose", "why", "how",
        "about", "tell", "explain", "describe", "information", "know", "understand",
        "me", "my", "i", "we", "our", "us", "you", "your", "he", "she", "it", "they", "them",
        "this", "that", "these", "those", "is", "are", "was", "were", "be", "been", "being",
        "have", "has", "had", "do", "does", "did", "can", "could", "will", "would", "shall", "should",
        "may", "might", "must", "for", "of", "with", "without", "in", "on", "at", "by", "to", "from"
    }

    keywords = [word for word in words if word not in stop_words and len(word) > 2]

    # If no keywords found, use the original words
    if not keywords:
        keywords = [word for word in words if len(word) > 2]

    return keywords

def reload_env_settings():
    """
    Reload environment settings from .env file.
    This ensures we get the latest settings even if they were updated after the process started.
    """
    try:
        from dotenv import load_dotenv
        load_dotenv(override=True)  # Override existing environment variables
    except ImportError:
        # If python-dotenv is not available, try to read the .env file manually
        try:
            with open('.env', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        except FileNotFoundError:
            pass
    except Exception as e:
        logger.warning(f"Could not reload environment settings: {e}")

def get_response_config(response_length: str) -> Dict[str, Any]:
    """Get configuration for different response lengths."""
    configs = {
        "short": {
            "max_tokens": 200,
            "instruction": "Provide a concise, direct answer in 1-2 sentences.",
            "format": "Brief summary only."
        },
        "brief": {
            "max_tokens": 500,
            "instruction": "Provide a clear, informative answer with key points.",
            "format": "Include main points with citations [1], [2], etc."
        },
        "detailed": {
            "max_tokens": 2000,
            "instruction": "Provide a comprehensive, well-structured response with detailed explanations.",
            "format": "Use headings, detailed explanations, mechanisms, and proper citations."
        }
    }
    return configs.get(response_length, configs["brief"])

def create_optimized_prompt(question: str, context: str, response_length: str, conversation_context: List[Dict[str, str]] = None) -> str:
    """Create an optimized prompt based on response length and conversation context."""
    config = get_response_config(response_length)

    # Build conversation history if provided
    conversation_history = ""
    if conversation_context and len(conversation_context) > 0:
        conversation_history = "\nConversation History:\n"
        for msg in conversation_context[-4:]:  # Last 4 messages for context
            role = msg.get('role', 'user')
            content = msg.get('content', '')[:200]  # Truncate for efficiency
            conversation_history += f"{role.title()}: {content}\n"
        conversation_history += "\n"

    # Create the optimized prompt
    prompt = f"""{conversation_history}Question: {question}

Context:
{context}

Instructions: {config['instruction']} {config['format']} Use numbered references [1], [2], etc. for citations.

Answer:"""

    return prompt

async def generate_answer(question: str, facts: List[Dict[str, Any]], llm_provider: str = None, llm_model: str = None, temperature: float = None, max_tokens: int = None, top_k: int = None, top_p: float = None, response_length: str = "brief", conversation_context: List[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    Generate an answer to a question.

    Args:
        question: Question
        facts: Relevant facts
        llm_provider: LLM provider
        llm_model: LLM model

    Returns:
        Answer
    """
    # Reload environment settings to get the latest values
    reload_env_settings()

    # Get LLM settings from environment variables if not provided
    if llm_provider is None:
        llm_provider = os.getenv("QA_LLM_PROVIDER", "openrouter")
    if llm_model is None:
        llm_model = os.getenv("QA_LLM_MODEL", "meta-llama/llama-4-maverick")
    if temperature is None:
        temperature = float(os.getenv("QA_LLM_TEMPERATURE", "0.3"))
    if top_k is None:
        top_k = int(os.getenv("QA_LLM_TOP_K", "40"))
    if top_p is None:
        top_p = float(os.getenv("QA_LLM_TOP_P", "0.9"))

    # Get response configuration and override max_tokens if not provided
    response_config = get_response_config(response_length)
    if max_tokens is None:
        max_tokens = response_config["max_tokens"]

    logger.info(f"Using LLM provider: {llm_provider}, model: {llm_model}, temperature: {temperature}, max_tokens: {max_tokens}, top_k: {top_k}, top_p: {top_p}")

    try:
        # Prepare the context
        context = ""
        for i, fact in enumerate(facts):
            # Clean up the fact body to remove image references and OCR metadata
            body = fact['body']

            # Remove image references like 'img-2.jpeg'
            body = re.sub(r'img-\d+\.jpeg', '[Image reference]', body)

            # Also remove any markdown image syntax
            body = re.sub(r'!\[.*?\]\(.*?\)', '[Image reference]', body)

            # Remove OCR metadata
            body = re.sub(r'OCRPageObject\(.*?\)', '', body)
            body = re.sub(r'OCRImageObject\(.*?\)', '', body)
            body = re.sub(r'OCRPageDimensions\(.*?\)', '', body)
            body = re.sub(r'images=\[.*?\]', '', body)
            body = re.sub(r'dimensions=.*?(?=\)|\,)', '', body)
            body = re.sub(r'index=\d+', '', body)
            body = re.sub(r'markdown=["\'](.*?)["\']', r'\1', body)

            # Remove extra parentheses and brackets
            body = re.sub(r'\(\)', '', body)
            body = re.sub(r'\[\]', '', body)

            # Remove any remaining OCR artifacts
            body = re.sub(r'pages=\[.*?\]', '', body)
            body = re.sub(r'\\\w+', ' ', body)  # Remove escaped characters like \n, \t, etc.
            body = re.sub(r'\$(.+?)\$', r'\1', body)  # Remove LaTeX-style math delimiters

            # Clean up multiple spaces and newlines
            body = re.sub(r'\s+', ' ', body)
            body = re.sub(r'\\n', '\n', body)

            # Fix common OCR issues
            body = re.sub(r'(?<=[a-z])\.(?=[A-Z])', '. ', body)  # Add space after period if missing
            body = re.sub(r'(?<=[a-z])(?=[A-Z])', ' ', body)  # Add space between words if missing

            # Trim the body
            body = body.strip()

            # Add a reference citation
            if 'chunk_num' in fact:
                # Format as a proper academic citation
                document_title = fact.get('document_title')
                chunk_num = fact.get('chunk_num')

                # Create a proper scientific citation with document ID for better reference tracking
                if document_title and isinstance(document_title, str):
                    # Clean up the document title
                    document_title = document_title.replace('.pdf', '')
                    # If it starts with a number followed by space, make it more readable
                    document_title = re.sub(r'^(\d+)\s+', '', document_title)

                    # Include document ID in the citation for better tracking
                    document_id = fact.get('document_id', '')
                    document_author = fact.get('document_author', '')
                    document_year = fact.get('document_year', '')

                    citation = f" (Source: {document_title}"

                    # Add author and year if available
                    if document_author and document_author != "Unknown author":
                        citation += f", {document_author}"
                        if document_year:
                            citation += f" {document_year}"

                    if chunk_num:
                        citation += f", Section {chunk_num}"

                    # Add document ID as a reference ID
                    if document_id:
                        citation += f", DocID: {document_id}"

                    citation += ")"
                else:
                    # Fallback to a generic citation with just the chunk number
                    if chunk_num:
                        citation = f" (Source: Document Section {chunk_num})"
                    else:
                        citation = " (Source: Unknown document)"
                body += citation

            context += f"[{i+1}] {body}\n\n"

        # Prepare the optimized prompt
        prompt = create_optimized_prompt(question, context, response_length, conversation_context)

        # Call the LLM
        answer = await call_llm(prompt, llm_provider, llm_model, facts, temperature, max_tokens, top_k, top_p)

        # Extract references
        references = extract_references(answer)

        # Map reference numbers to facts
        referenced_facts = []
        max_ref = max(references) if references else 0

        # Log the references found in the answer
        logger.info(f"References found in answer: {references}")
        logger.info(f"Number of facts available: {len(facts)}")

        # If the LLM mentions more references than we have facts, add a warning
        if max_ref > len(facts):
            logger.warning(f"LLM mentioned references up to [{max_ref}] but we only have {len(facts)} facts")

        # Include all facts that are referenced in the answer
        for ref_num in references:
            if 1 <= ref_num <= len(facts):
                referenced_facts.append(facts[ref_num - 1])
            else:
                # For references that don't exist in our facts, create a placeholder
                logger.warning(f"Reference [{ref_num}] mentioned in answer but not found in facts")

                # If we have at least one fact, use its structure as a template
                if facts:
                    placeholder_fact = facts[0].copy()
                    placeholder_fact.update({
                        "uuid": f"placeholder-{ref_num}",
                        "body": f"Reference [{ref_num}] was mentioned in the answer but the source was not found in the knowledge graph.",
                        "chunk_num": None,
                        "document_id": None,
                        "document_title": "Missing Reference",
                        "document_name": "Missing Reference",
                        "document_author": "",
                        "document_year": ""
                    })
                    referenced_facts.append(placeholder_fact)

        # If no references were found but we have facts, include the first fact as a fallback
        if not referenced_facts and facts:
            logger.warning("No references found in answer, including first fact as fallback")
            referenced_facts.append(facts[0])

        return {
            "question": question,
            "answer": answer,
            "references": referenced_facts
        }

    except Exception as e:
        logger.error(f"Error generating answer: {e}")
        return {
            "question": question,
            "answer": f"Error generating answer: {str(e)}",
            "references": []
        }

def extract_references(text: str) -> List[int]:
    """
    Extract reference numbers from text.

    Args:
        text: Text to extract references from

    Returns:
        List of reference numbers
    """
    # Find all references in the format [n]
    references = re.findall(r'\[(\d+)\]', text)

    # Convert to integers and deduplicate
    reference_numbers = []
    for ref in references:
        try:
            ref_num = int(ref)
            if ref_num not in reference_numbers:
                reference_numbers.append(ref_num)
        except ValueError:
            pass

    return reference_numbers

async def call_llm(prompt: str, provider: str, model: str, facts: List[Dict[str, Any]] = None, temperature: float = 0.3, max_tokens: int = 1000, top_k: int = 40, top_p: float = 0.9) -> str:
    """Call an LLM.

    Args:
        prompt: Prompt
        provider: LLM provider
        model: LLM model
        facts: Relevant facts (optional, used for fallback)

    Returns:
        LLM response
    """
    try:
        # Import the appropriate LLM module based on the provider
        if provider == "openai":
            from openai import OpenAI

            # Initialize the client
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            # Call the API
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are Avery, a health knowledge assistant. Provide evidence-based responses with proper citations [1], [2], etc. Be accurate, concise, and cite sources."},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Extract the answer
            try:
                return response.choices[0].message.content
            except Exception as e:
                logger.error(f"Error extracting OpenAI response: {e}")
                logger.info(f"OpenAI response: {response}")
                return f"Error extracting response from OpenAI: {str(e)}"

        elif provider == "openrouter":
            import requests

            # Check if the API key is set (try both naming conventions)
            api_key = os.getenv('OPENROUTER_API_KEY') or os.getenv('OPEN_ROUTER_API_KEY')
            if not api_key:
                logger.warning(f"API key environment variables checked: OPENROUTER_API_KEY={os.getenv('OPENROUTER_API_KEY')}, OPEN_ROUTER_API_KEY={os.getenv('OPEN_ROUTER_API_KEY')}")
                logger.error("OpenRouter API key is not set, using fallback approach")

                # Use a simple approach to generate an answer based on the facts
                if facts:
                    answer = "Based on the information in the knowledge graph:\n\n"

                    for i, fact in enumerate(facts):
                        # Clean up the fact body to remove image references and OCR metadata
                        body = fact['body']

                        # Remove image references like 'img-2.jpeg'
                        body = re.sub(r'img-\d+\.jpeg', '[Image reference]', body)

                        # Also remove any markdown image syntax
                        body = re.sub(r'!\[.*?\]\(.*?\)', '[Image reference]', body)

                        # Remove OCR metadata
                        body = re.sub(r'OCRPageObject\(.*?\)', '', body)
                        body = re.sub(r'OCRImageObject\(.*?\)', '', body)
                        body = re.sub(r'OCRPageDimensions\(.*?\)', '', body)
                        body = re.sub(r'images=\[.*?\]', '', body)
                        body = re.sub(r'dimensions=.*?(?=\)|\,)', '', body)
                        body = re.sub(r'index=\d+', '', body)
                        body = re.sub(r'markdown=["\'](.*?)["\']', r'\1', body)

                        # Remove extra parentheses and brackets
                        body = re.sub(r'\(\)', '', body)
                        body = re.sub(r'\[\]', '', body)

                        # Remove any remaining OCR artifacts
                        body = re.sub(r'pages=\[.*?\]', '', body)
                        body = re.sub(r'\\\w+', ' ', body)  # Remove escaped characters like \n, \t, etc.
                        body = re.sub(r'\$(.+?)\$', r'\1', body)  # Remove LaTeX-style math delimiters

                        # Clean up multiple spaces and newlines
                        body = re.sub(r'\s+', ' ', body)
                        body = re.sub(r'\\n', '\n', body)

                        # Fix common OCR issues
                        body = re.sub(r'(?<=[a-z])\.(?=[A-Z])', '. ', body)  # Add space after period if missing
                        body = re.sub(r'(?<=[a-z])(?=[A-Z])', ' ', body)  # Add space between words if missing

                        # Trim the body
                        body = body.strip()

                        # Add a reference citation
                        if 'chunk_num' in fact:
                            # Format as a proper academic citation
                            document_title = fact.get('document_title')
                            chunk_num = fact.get('chunk_num')

                            # Create a proper scientific citation with document ID for better reference tracking
                            if document_title and isinstance(document_title, str):
                                # Clean up the document title
                                document_title = document_title.replace('.pdf', '')
                                # If it starts with a number followed by space, make it more readable
                                document_title = re.sub(r'^(\d+)\s+', '', document_title)

                                # Include document ID in the citation for better tracking
                                document_id = fact.get('document_id', '')
                                document_author = fact.get('document_author', '')
                                document_year = fact.get('document_year', '')

                                citation = f" (Source: {document_title}"

                                # Add author and year if available
                                if document_author and document_author != "Unknown author":
                                    citation += f", {document_author}"
                                    if document_year:
                                        citation += f" {document_year}"

                                if chunk_num:
                                    citation += f", Section {chunk_num}"

                                # Add document ID as a reference ID
                                if document_id:
                                    citation += f", DocID: {document_id}"

                                citation += ")"
                            else:
                                # Fallback to a generic citation with just the chunk number
                                if chunk_num:
                                    citation = f" (Source: Document Section {chunk_num})"
                                else:
                                    citation = " (Source: Unknown document)"
                            body += citation

                        answer += f"[{i+1}] {body}\n\n"

                    return answer
                else:
                    return "I don't have enough information to answer this question. The OpenRouter API key is not set, and no facts were found in the knowledge graph."

            # Prepare the request
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": "You are Avery, a health knowledge assistant. Provide evidence-based responses with proper citations [1], [2], etc. Be accurate, concise, and cite sources."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            # Call the API
            try:
                response = requests.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                # Check if the response is successful
                if response.status_code != 200:
                    logger.error(f"OpenRouter API returned status code {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    return f"Error: OpenRouter API returned status code {response.status_code}. Response: {response.text}"
            except Exception as e:
                logger.error(f"Error calling OpenRouter API: {e}")
                return f"Error calling OpenRouter API: {str(e)}"

            # Extract the answer
            response_json = response.json()
            logger.info(f"OpenRouter response: {response_json}")

            # Check if the response has the expected structure
            if "choices" in response_json and len(response_json["choices"]) > 0:
                return response_json["choices"][0]["message"]["content"]
            elif "error" in response_json:
                return f"Error from OpenRouter: {response_json['error']}"
            else:
                logger.error(f"Unexpected response format: {response_json}")
                return "Error: Unexpected response format from OpenRouter"

        elif provider == "ollama":
            import requests
            import json

            # Get Ollama base URL
            ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

            # Prepare the request
            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": "You are Avery, a health knowledge assistant. Provide evidence-based responses with proper citations [1], [2], etc. Be accurate, concise, and cite sources."},
                    {"role": "user", "content": prompt}
                ],
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens,
                    "top_k": top_k,
                    "top_p": top_p
                }
            }

            # Call the API
            try:
                response = requests.post(
                    f"{ollama_base_url}/api/chat",
                    headers=headers,
                    json=data
                )

                # Check if the response is successful
                if response.status_code != 200:
                    logger.error(f"Ollama API returned status code {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    return f"Error: Ollama API returned status code {response.status_code}. Response: {response.text}"
            except Exception as e:
                logger.error(f"Error calling Ollama API: {e}")
                return f"Error calling Ollama API: {str(e)}"

            # Extract the answer
            try:
                response_json = response.json()
                logger.info(f"Ollama response: {response_json}")

                # Check if the response has the expected structure
                if "message" in response_json and "content" in response_json["message"]:
                    return response_json["message"]["content"]
                else:
                    logger.error(f"Unexpected response format: {response_json}")
                    return "Error: Unexpected response format from Ollama"
            except Exception as e:
                logger.error(f"Error parsing Ollama response: {e}")
                return f"Error parsing Ollama response: {str(e)}"

        else:
            return f"Unsupported LLM provider: {provider}"

    except Exception as e:
        logger.error(f"Error calling LLM: {e}")
        return f"Error calling LLM: {str(e)}"
