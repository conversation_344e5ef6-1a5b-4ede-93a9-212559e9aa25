/**
 * Entities handling for Graphiti Flask Web Interface
 *
 * This script handles the entities functionality in the Entities tab.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Flask Entities.js loaded");

    // Load entities when the entities tab is shown
    document.getElementById('entities-tab').addEventListener('click', function() {
        loadEntities();
    });

    // Add event listener for the entity search button
    document.getElementById('entity-search-button').addEventListener('click', function() {
        loadEntities();
    });

    // Add event listener for Enter key in the entity search input
    document.getElementById('entity-search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loadEntities();
        }
    });

    // Add event listeners for the entity type filter and min mentions filter
    document.getElementById('entity-type-filter').addEventListener('change', function() {
        loadEntities();
    });

    document.getElementById('min-mentions-filter').addEventListener('change', function() {
        loadEntities();
    });
});

/**
 * Load entities from the server
 *
 * @param {number} page - The page number to load (1-based)
 * @param {number} limit - The number of entities per page
 */
function loadEntities(page = 1, limit = 100) {
    // Get the search query
    const searchInput = document.getElementById('entity-search-input');
    const query = searchInput.value.trim();

    // Get the entity type filter
    const entityTypeFilter = document.getElementById('entity-type-filter');
    const entityType = entityTypeFilter.value;

    // Store the entity type in a global variable
    window.currentEntityType = entityType;

    // Get the min mentions filter
    const minMentionsFilter = document.getElementById('min-mentions-filter');
    const minMentions = minMentionsFilter.value;

    // Calculate offset based on page and limit
    const offset = (page - 1) * limit;

    // Store current page and limit in global variables
    window.currentPage = page;
    window.pageLimit = limit;

    // Show the loading indicator
    document.getElementById('entities-loading').style.display = 'block';

    // Clear previous results
    document.getElementById('entities-list').innerHTML = '';

    // Build the query string and determine the URL
    let url;
    let queryString = '';

    if (entityType) {
        // If a specific entity type is selected, use the entity type endpoint with all=true
        url = `/api/entities/${encodeURIComponent(entityType)}`;
        queryString = 'all=true';  // Get all entities of this type
    } else {
        // Otherwise, use the main entities endpoint with pagination
        url = '/api/entities';

        if (query) {
            queryString += `q=${encodeURIComponent(query)}`;
        }

        if (minMentions) {
            queryString += queryString ? '&' : '';
            queryString += `min_mentions=${encodeURIComponent(minMentions)}`;
        }

        // Add pagination parameters
        queryString += queryString ? '&' : '';
        queryString += `offset=${offset}&limit=${limit}`;
    }

    // Add the query string to the URL
    url = `${url}${queryString ? '?' + queryString : ''}`;
    console.log(`Fetching entities from: ${url} (page ${page}, limit ${limit}, entityType: ${entityType || 'all'})`);

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide the loading indicator
            document.getElementById('entities-loading').style.display = 'none';

            console.log('Received data from API:', data);

            // Check if data contains entities property
            if (data && data.entities) {
                console.log(`Found ${data.entities.length} entities out of ${data.count} total`);
                // Store the data globally for access in other functions
                window.entityData = data;
                // Display the entities
                displayEntities(data.entities);
            } else {
                console.error('Invalid data format, missing entities property:', data);
                document.getElementById('entities-list').innerHTML = `
                    <div class="alert alert-warning" role="alert">
                        No entities found or invalid data format.
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide the loading indicator
            document.getElementById('entities-loading').style.display = 'none';

            // Display an error message
            document.getElementById('entities-list').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    Error loading entities: ${error.message}
                </div>
            `;
        });
}

/**
 * Display entities
 *
 * @param {Array} entities - The entities to display
 */
function displayEntities(entities) {
    // Get the entities container
    const entitiesContainer = document.getElementById('entities-list');

    // Clear the container first
    entitiesContainer.innerHTML = '';

    // Check if entities is an array
    if (!entities || !Array.isArray(entities) || entities.length === 0) {
        entitiesContainer.innerHTML = `
            <div class="alert alert-info" role="alert">
                No entities found.
            </div>
        `;
        return;
    }

    // Add pagination controls and page size selector at the top
    if (window.entityData && window.entityData.pagination) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'd-flex justify-content-between align-items-center my-4';

        // Add page size selector
        const pageSizeContainer = document.createElement('div');
        pageSizeContainer.className = 'page-size-container';

        const pageSizeLabel = document.createElement('label');
        pageSizeLabel.className = 'me-2';
        pageSizeLabel.textContent = 'Entities per page:';
        pageSizeLabel.setAttribute('for', 'page-size-selector');

        const pageSizeSelect = document.createElement('select');
        pageSizeSelect.className = 'form-select form-select-sm';
        pageSizeSelect.id = 'page-size-selector';

        const pageSizes = [50, 100, 200, 500, 1000];
        pageSizes.forEach(size => {
            const option = document.createElement('option');
            option.value = size;
            option.textContent = size;
            if (size === window.pageLimit) {
                option.selected = true;
            }
            pageSizeSelect.appendChild(option);
        });

        pageSizeSelect.addEventListener('change', function() {
            const newLimit = parseInt(this.value);
            loadEntities(1, newLimit);
        });

        pageSizeContainer.appendChild(pageSizeLabel);
        pageSizeContainer.appendChild(pageSizeSelect);

        // Add pagination controls
        const paginationTop = createPaginationControls(window.entityData.pagination);

        paginationContainer.appendChild(pageSizeContainer);
        paginationContainer.appendChild(paginationTop);

        entitiesContainer.appendChild(paginationContainer);
    }

    console.log("Displaying entities:", entities);

    // Group entities by type
    const entityTypes = {};
    entities.forEach(entity => {
        const type = entity.type || 'Unknown';
        if (!entityTypes[type]) {
            entityTypes[type] = [];
        }
        entityTypes[type].push(entity);
    });

    // Create a container for all entity types
    const container = document.createElement('div');
    container.className = 'container-fluid p-0';
    entitiesContainer.appendChild(container);

    // Get sorted entity types
    const sortedTypes = Object.keys(entityTypes).sort();

    // Create a summary section
    const summarySection = document.createElement('div');
    summarySection.className = 'mb-4 p-3 bg-primary bg-opacity-10 border border-primary rounded';

    // Create a summary header
    const summaryHeader = document.createElement('h4');
    summaryHeader.className = 'text-primary';
    summaryHeader.textContent = 'Entities Summary';
    summarySection.appendChild(summaryHeader);

    // Create a summary description
    const summaryDesc = document.createElement('p');
    // Use the total count from the API if available
    const totalCount = window.entityData && window.entityData.count ? window.entityData.count : entities.length;

    // Special case for when we're viewing a specific entity type
    if (window.currentEntityType) {
        const typeCount = window.entityData && window.entityData.type_counts &&
                         window.entityData.type_counts[window.currentEntityType] ?
                         window.entityData.type_counts[window.currentEntityType] : entities.length;

        if (entities.length === typeCount) {
            // We're showing all entities of this type
            summaryDesc.textContent = `Showing all ${entities.length} ${window.currentEntityType} entities with 0+ mentions, sorted by name`;
        } else {
            // We're showing a subset of entities of this type
            summaryDesc.textContent = `Showing ${entities.length} of ${typeCount} ${window.currentEntityType} entities with 0+ mentions, sorted by name`;
        }
    } else {
        // We're viewing all entity types
        summaryDesc.textContent = `Showing ${entities.length} of ${totalCount} entities with 0+ mentions, sorted by name`;
    }

    summarySection.appendChild(summaryDesc);

    // Create a summary list
    const summaryList = document.createElement('div');
    summaryList.className = 'd-flex flex-wrap gap-2';

    // Add each entity type to the summary
    sortedTypes.forEach(type => {
        const typeItem = document.createElement('div');
        typeItem.className = 'badge bg-primary p-2 fs-6';

        // Use the accurate count from the API if available
        let count = entityTypes[type].length;
        if (window.entityData && window.entityData.type_counts && window.entityData.type_counts[type]) {
            count = window.entityData.type_counts[type];
        }

        typeItem.textContent = `${type} (${count})`;
        summaryList.appendChild(typeItem);
    });

    summarySection.appendChild(summaryList);
    container.appendChild(summarySection);

    // Create sections for each entity type
    sortedTypes.forEach(type => {
        // Create a section for this entity type
        const typeSection = document.createElement('div');
        typeSection.className = 'mb-4';

        // Create a header for the entity type
        const typeHeader = document.createElement('div');
        typeHeader.className = 'mb-2 bg-primary text-white p-2 rounded';

        // Use the accurate count from the API if available
        let count = entityTypes[type].length;
        if (window.entityData && window.entityData.type_counts && window.entityData.type_counts[type]) {
            count = window.entityData.type_counts[type];
        }

        typeHeader.innerHTML = `
            <h5 class="mb-0">${type} (${count})</h5>
        `;
        typeSection.appendChild(typeHeader);

        // Create a list for entities
        const entityList = document.createElement('div');
        entityList.className = 'row row-cols-1 row-cols-md-3 row-cols-lg-4 g-3';

        // Add each entity as a card
        entityTypes[type].forEach(entity => {
            const entityCol = document.createElement('div');
            entityCol.className = 'col';

            const entityCard = document.createElement('div');
            entityCard.className = 'card h-100';
            entityCard.style.cursor = 'pointer';
            entityCard.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            entityCard.style.transition = 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out';

            // Add hover effect
            entityCard.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 4px 8px rgba(13, 110, 253, 0.25)';
                this.style.borderColor = '#0d6efd';
            });

            entityCard.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                this.style.borderColor = '#dee2e6';
            });

            // Add click event to show entity details
            entityCard.addEventListener('click', function(e) {
                e.preventDefault();
                showEntityDetails(entity.uuid);
            });

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-2';

            const entityName = document.createElement('div');
            entityName.className = 'fw-bold text-truncate';
            entityName.title = entity.name;
            entityName.textContent = entity.name;

            const mentionCount = document.createElement('div');
            mentionCount.className = 'badge bg-primary rounded-pill float-end';
            mentionCount.textContent = entity.mention_count || 0;

            cardBody.appendChild(entityName);
            cardBody.appendChild(mentionCount);
            entityCard.appendChild(cardBody);
            entityCol.appendChild(entityCard);

            entityList.appendChild(entityCol);
        });

        typeSection.appendChild(entityList);
        container.appendChild(typeSection);
    });

    // Add pagination controls at the bottom
    if (window.entityData && window.entityData.pagination) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'd-flex justify-content-center my-4';

        const paginationBottom = createPaginationControls(window.entityData.pagination);
        paginationContainer.appendChild(paginationBottom);

        container.appendChild(paginationContainer);
    }
}

/**
 * Create pagination controls
 *
 * @param {Object} pagination - Pagination metadata
 * @returns {HTMLElement} - Pagination controls container
 */
function createPaginationControls(pagination) {
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-container';

    const nav = document.createElement('nav');
    nav.setAttribute('aria-label', 'Entity pagination');

    const ul = document.createElement('ul');
    ul.className = 'pagination pagination-sm';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.has_prev ? '' : 'disabled'}`;

    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.textContent = 'Previous';

    if (pagination.has_prev) {
        prevLink.addEventListener('click', function(e) {
            e.preventDefault();
            loadEntities(pagination.page - 1, pagination.limit);
        });
    }

    prevLi.appendChild(prevLink);
    ul.appendChild(prevLi);

    // Page numbers
    const totalPages = pagination.total_pages;
    const currentPage = pagination.page;

    // Determine which page numbers to show
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    // Adjust if we're near the end
    if (endPage - startPage < 4 && startPage > 1) {
        startPage = Math.max(1, endPage - 4);
    }

    // First page button if not starting from page 1
    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';

        const firstLink = document.createElement('a');
        firstLink.className = 'page-link';
        firstLink.href = '#';
        firstLink.textContent = '1';

        firstLink.addEventListener('click', function(e) {
            e.preventDefault();
            loadEntities(1, pagination.limit);
        });

        firstLi.appendChild(firstLink);
        ul.appendChild(firstLi);

        // Add ellipsis if needed
        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';

            const ellipsisSpan = document.createElement('span');
            ellipsisSpan.className = 'page-link';
            ellipsisSpan.textContent = '...';

            ellipsisLi.appendChild(ellipsisSpan);
            ul.appendChild(ellipsisLi);
        }
    }

    // Page number buttons
    for (let i = startPage; i <= endPage; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;

        const pageLink = document.createElement('a');
        pageLink.className = 'page-link';
        pageLink.href = '#';
        pageLink.textContent = i;

        if (i !== currentPage) {
            pageLink.addEventListener('click', function(e) {
                e.preventDefault();
                loadEntities(i, pagination.limit);
            });
        }

        pageLi.appendChild(pageLink);
        ul.appendChild(pageLi);
    }

    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';

            const ellipsisSpan = document.createElement('span');
            ellipsisSpan.className = 'page-link';
            ellipsisSpan.textContent = '...';

            ellipsisLi.appendChild(ellipsisSpan);
            ul.appendChild(ellipsisLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';

        const lastLink = document.createElement('a');
        lastLink.className = 'page-link';
        lastLink.href = '#';
        lastLink.textContent = totalPages;

        lastLink.addEventListener('click', function(e) {
            e.preventDefault();
            loadEntities(totalPages, pagination.limit);
        });

        lastLi.appendChild(lastLink);
        ul.appendChild(lastLi);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.has_next ? '' : 'disabled'}`;

    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.textContent = 'Next';

    if (pagination.has_next) {
        nextLink.addEventListener('click', function(e) {
            e.preventDefault();
            loadEntities(pagination.page + 1, pagination.limit);
        });
    }

    nextLi.appendChild(nextLink);
    ul.appendChild(nextLi);

    nav.appendChild(ul);
    paginationContainer.appendChild(nav);

    return paginationContainer;
}

/**
 * Show entity details
 *
 * @param {string} uuid - The UUID of the entity
 */
function showEntityDetails(uuid) {
    // Call the API to get entity details
    fetch(`/api/entities/${uuid}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(entity => {
            // Create a modal to display the entity details
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'entityModal';
            modal.tabIndex = '-1';
            modal.setAttribute('aria-labelledby', 'entityModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            // Create the modal content
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="entityModalLabel">${entity.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Type:</strong> ${entity.type || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Mentions:</strong> ${entity.mentions || 0}</p>
                                </div>
                            </div>

                            <h6>Mentions in Documents:</h6>
                            <ul class="list-group mb-3">
                                ${entity.documents ? entity.documents.map(doc => `
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        ${doc.document}
                                        <span class="badge bg-primary rounded-pill">${doc.count}</span>
                                    </li>
                                `).join('') : '<li class="list-group-item">No document mentions found.</li>'}
                            </ul>

                            <h6>Related Entities:</h6>
                            <ul class="list-group">
                                ${entity.related ? entity.related.map(rel => `
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        ${rel.name} (${rel.type})
                                        <span class="badge bg-secondary rounded-pill">${rel.strength}</span>
                                    </li>
                                `).join('') : '<li class="list-group-item">No related entities found.</li>'}
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Add the modal to the document
            document.body.appendChild(modal);

            // Show the modal
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // Remove the modal from the DOM when it's hidden
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        })
        .catch(error => {
            console.error('Error:', error);

            // Display an error message
            alert(`Error loading entity details: ${error.message}`);
        });
}
