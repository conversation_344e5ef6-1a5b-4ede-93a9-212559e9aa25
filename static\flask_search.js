/**
 * Search handling for Graphiti Flask Web Interface
 * 
 * This script handles the search functionality in the Search tab.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Flask Search.js loaded");
    
    // Add event listener for the search button
    document.getElementById('search-button').addEventListener('click', function() {
        performSearch();
    });
    
    // Add event listener for Enter key in the search input
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});

/**
 * Perform a search based on the selected search type
 */
function performSearch() {
    // Get the search query
    const searchInput = document.getElementById('search-input');
    const query = searchInput.value.trim();
    
    // If the query is empty, do nothing
    if (!query) {
        return;
    }
    
    // Get the selected search type
    const searchType = document.querySelector('input[name="searchType"]:checked').value;
    
    // Show the loading indicator
    document.getElementById('search-loading').style.display = 'block';
    
    // Clear previous results
    document.getElementById('search-results').innerHTML = '';
    
    // Perform the search based on the selected type
    if (searchType === 'semantic') {
        performSemanticSearch(query);
    } else if (searchType === 'hybrid') {
        performHybridSearch(query);
    }
}

/**
 * Perform a semantic search
 * 
 * @param {string} query - The search query
 */
function performSemanticSearch(query) {
    // Call the API to perform a semantic search
    fetch(`/api/semantic?q=${encodeURIComponent(query)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide the loading indicator
            document.getElementById('search-loading').style.display = 'none';
            
            // Display the results
            displaySearchResults(data, query);
        })
        .catch(error => {
            console.error('Error:', error);
            
            // Hide the loading indicator
            document.getElementById('search-loading').style.display = 'none';
            
            // Display an error message
            document.getElementById('search-results').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    Error performing search: ${error.message}
                </div>
            `;
        });
}

/**
 * Perform a hybrid search
 * 
 * @param {string} query - The search query
 */
function performHybridSearch(query) {
    // Call the API to perform a hybrid search
    fetch(`/api/hybrid?q=${encodeURIComponent(query)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide the loading indicator
            document.getElementById('search-loading').style.display = 'none';
            
            // Display the results
            displaySearchResults(data, query);
        })
        .catch(error => {
            console.error('Error:', error);
            
            // Hide the loading indicator
            document.getElementById('search-loading').style.display = 'none';
            
            // Display an error message
            document.getElementById('search-results').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    Error performing search: ${error.message}
                </div>
            `;
        });
}

/**
 * Display search results
 * 
 * @param {Array} results - The search results
 * @param {string} query - The search query
 */
function displaySearchResults(results, query) {
    // Get the results container
    const resultsContainer = document.getElementById('search-results');
    
    // If there are no results, display a message
    if (!results || results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="alert alert-info" role="alert">
                No results found for "${query}".
            </div>
        `;
        return;
    }
    
    // Create a header for the results
    const header = document.createElement('h4');
    header.textContent = `Search Results for "${query}"`;
    resultsContainer.appendChild(header);
    
    // Create a container for the results
    const resultsListContainer = document.createElement('div');
    resultsListContainer.className = 'mt-3';
    
    // Add each result to the container
    results.forEach((result, index) => {
        const resultCard = document.createElement('div');
        resultCard.className = 'card result-card mb-3';
        
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        
        // Add the document title
        const documentTitle = document.createElement('h5');
        documentTitle.className = 'card-title';
        documentTitle.textContent = result.document || 'Unknown Document';
        
        // Add the score badge
        const scoreBadge = document.createElement('span');
        scoreBadge.className = 'badge bg-primary score-badge';
        scoreBadge.textContent = `Score: ${parseFloat(result.score).toFixed(2)}`;
        documentTitle.appendChild(scoreBadge);
        
        // Add the preview text
        const previewText = document.createElement('p');
        previewText.className = 'card-text';
        previewText.textContent = result.preview || 'No preview available';
        
        // Add the source link
        const sourceLink = document.createElement('a');
        sourceLink.className = 'card-link source-link';
        sourceLink.href = '#';
        sourceLink.textContent = 'View Source';
        sourceLink.addEventListener('click', function(e) {
            e.preventDefault();
            // TODO: Implement view source functionality
            alert(`View source for ${result.document}`);
        });
        
        // Add all elements to the card
        cardBody.appendChild(documentTitle);
        cardBody.appendChild(previewText);
        cardBody.appendChild(sourceLink);
        resultCard.appendChild(cardBody);
        
        // Add the card to the results container
        resultsListContainer.appendChild(resultCard);
    });
    
    // Add the results list to the container
    resultsContainer.appendChild(resultsListContainer);
}
