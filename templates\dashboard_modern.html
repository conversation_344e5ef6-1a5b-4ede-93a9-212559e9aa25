{% extends "base_modern.html" %}

{% block title %}Dashboard - Graphiti{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Dashboard</h1>
        <p class="text-muted">Welcome to your Knowledge Graph Platform</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="refreshDashboard()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card gradient-card stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-documents">-</div>
                <div class="stats-label">
                    <i class="bi bi-file-earmark-text"></i> Documents
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-success stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-entities">-</div>
                <div class="stats-label">
                    <i class="bi bi-diagram-3"></i> Entities
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-warning stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-references">-</div>
                <div class="stats-label">
                    <i class="bi bi-bookmark"></i> References
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-info stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-relationships">-</div>
                <div class="stats-label">
                    <i class="bi bi-share"></i> Relationships
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/enhanced-upload" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="bi bi-cloud-upload fs-1 mb-2"></i>
                            <span>Enhanced Upload</span>
                            <small class="text-muted">Multiple formats</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/search" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="bi bi-search fs-1 mb-2"></i>
                            <span>Search</span>
                            <small class="text-muted">Find content</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/qa" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="bi bi-chat-dots fs-1 mb-2"></i>
                            <span>Q&A</span>
                            <small class="text-muted">Ask questions</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/knowledge-graph" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="bi bi-share fs-1 mb-2"></i>
                            <span>Knowledge Graph</span>
                            <small class="text-muted">Explore connections</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity & System Status -->
<div class="row">
    <!-- Recent Documents -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-clock-history"></i> Recent Documents</h5>
                <a href="/documents" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div id="recent-documents">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="text-muted mt-2">Loading recent documents...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Entities -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-diagram-3"></i> Top Entities</h5>
                <a href="/entities" class="btn btn-sm btn-outline-success">View All</a>
            </div>
            <div class="card-body">
                <div id="top-entities">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="text-muted mt-2">Loading top entities...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-activity"></i> System Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-database text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">FalkorDB</h6>
                                <small class="text-success" id="falkordb-status">Connected</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-hdd-stack text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">Redis Vector</h6>
                                <small class="text-info" id="redis-status">Connected</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="bi bi-robot text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">Mistral OCR</h6>
                                <small class="text-warning" id="ocr-status">Ready</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

async function loadDashboardData() {
    try {
        // Load stats
        await Promise.all([
            loadStats(),
            loadRecentDocuments(),
            loadTopEntities(),
            loadSystemStatus()
        ]);
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showAlert('Error loading dashboard data', 'danger');
    }
}

async function loadStats() {
    try {
        const response = await fetch('/api/graph-stats');
        if (response.ok) {
            const stats = await response.json();

            document.getElementById('total-documents').textContent = stats.total_episodes || 0;
            document.getElementById('total-entities').textContent = stats.total_entities || 0;
            document.getElementById('total-references').textContent = stats.total_references || 0;
            document.getElementById('total-relationships').textContent = stats.total_relationships || 0;
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

async function loadRecentDocuments() {
    try {
        const response = await fetch('/api/documents?limit=5');
        if (response.ok) {
            const data = await response.json();
            const container = document.getElementById('recent-documents');

            if (data.documents && data.documents.length > 0) {
                container.innerHTML = data.documents.map(doc => `
                    <div class="d-flex align-items-center mb-3 slide-in">
                        <div class="me-3">
                            <i class="bi bi-file-earmark-text text-primary fs-4"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${doc.name || 'Untitled'}</h6>
                            <small class="text-muted">
                                ${doc.processed_at ? formatDate(doc.processed_at) : 'Unknown date'}
                            </small>
                        </div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted text-center">No documents found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading recent documents:', error);
        document.getElementById('recent-documents').innerHTML = '<p class="text-danger text-center">Error loading documents</p>';
    }
}

async function loadTopEntities() {
    try {
        const response = await fetch('/api/entities?limit=5&sort_by=mention_count&sort_order=desc');
        if (response.ok) {
            const data = await response.json();
            const container = document.getElementById('top-entities');

            if (data.entities && data.entities.length > 0) {
                container.innerHTML = data.entities.map(entity => `
                    <div class="d-flex align-items-center justify-content-between mb-3 slide-in">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <span class="badge bg-success">${entity.type || 'Entity'}</span>
                            </div>
                            <div>
                                <h6 class="mb-0">${entity.name || 'Unknown'}</h6>
                                <small class="text-muted">${entity.mention_count || 0} mentions</small>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted text-center">No entities found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading top entities:', error);
        document.getElementById('top-entities').innerHTML = '<p class="text-danger text-center">Error loading entities</p>';
    }
}

async function loadSystemStatus() {
    try {
        const response = await fetch('/api/system-status');
        if (response.ok) {
            const status = await response.json();

            // Update status indicators based on response
            if (status.falkordb_connected) {
                document.getElementById('falkordb-status').textContent = 'Connected';
                document.getElementById('falkordb-status').className = 'text-success';
            } else {
                document.getElementById('falkordb-status').textContent = 'Disconnected';
                document.getElementById('falkordb-status').className = 'text-danger';
            }

            if (status.redis_connected) {
                document.getElementById('redis-status').textContent = 'Connected';
                document.getElementById('redis-status').className = 'text-success';
            } else {
                document.getElementById('redis-status').textContent = 'Disconnected';
                document.getElementById('redis-status').className = 'text-danger';
            }
        }
    } catch (error) {
        console.error('Error loading system status:', error);
    }
}

function refreshDashboard() {
    const button = event.target;
    const hideLoading = showLoading(button);

    loadDashboardData().finally(() => {
        hideLoading();
        showAlert('Dashboard refreshed successfully', 'success', 2000);
    });
}
</script>
{% endblock %}
