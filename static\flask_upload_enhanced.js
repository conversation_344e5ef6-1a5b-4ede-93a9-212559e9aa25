/**
 * Enhanced Upload handling for Graphiti Flask Web Interface
 *
 * This script handles the upload functionality in the Upload tab with support for multiple file types.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced Upload.js loaded");

    // Constants
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const MAX_FILE_SIZE_DISPLAY = formatFileSize(MAX_FILE_SIZE);

    // Supported file types
    const SUPPORTED_FILE_TYPES = {
        // Document formats
        'application/pdf': { icon: 'bi-file-earmark-pdf', label: 'PDF' },
        'text/plain': { icon: 'bi-file-earmark-text', label: 'Text' },
        'text/markdown': { icon: 'bi-file-earmark-text', label: 'Markdown' },
        'application/rtf': { icon: 'bi-file-earmark-richtext', label: 'RTF' },
        'application/msword': { icon: 'bi-file-earmark-word', label: 'Word' },
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: 'bi-file-earmark-word', label: 'Word' },
        'application/vnd.oasis.opendocument.text': { icon: 'bi-file-earmark-text', label: 'ODT' },

        // Web formats
        'text/html': { icon: 'bi-file-earmark-code', label: 'HTML' },
        'application/xml': { icon: 'bi-file-earmark-code', label: 'XML' },

        // Spreadsheet formats
        'text/csv': { icon: 'bi-file-earmark-spreadsheet', label: 'CSV' },
        'application/vnd.ms-excel': { icon: 'bi-file-earmark-excel', label: 'Excel' },
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { icon: 'bi-file-earmark-excel', label: 'Excel' },

        // Presentation formats
        'application/vnd.ms-powerpoint': { icon: 'bi-file-earmark-slides', label: 'PowerPoint' },
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': { icon: 'bi-file-earmark-slides', label: 'PowerPoint' },

        // E-book formats
        'application/epub+zip': { icon: 'bi-book', label: 'EPUB' }
    };

    // Get DOM elements
    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('file-input');
    const selectFileButton = document.getElementById('select-file-button');
    const selectedFiles = document.getElementById('selected-files');
    const uploadButton = document.getElementById('upload-button');
    const uploadProgress = document.getElementById('upload-progress');
    const uploadStatus = document.getElementById('upload-status');
    const processingProgress = document.getElementById('processing-progress');
    const processingStatus = document.getElementById('processing-status');
    const processingStep = document.getElementById('processing-step');
    const uploadResults = document.getElementById('upload-results');
    const uploadResultsContent = document.getElementById('upload-results-content');
    const maxFileSizeInfo = document.getElementById('max-file-size-info');
    const supportedFormatsInfo = document.getElementById('supported-formats-info');

    // Progress tracking variables
    let currentDocumentId = null;
    let progressInterval = null;

    // Update the max file size info
    if (maxFileSizeInfo) {
        maxFileSizeInfo.textContent = `Maximum file size: ${MAX_FILE_SIZE_DISPLAY}`;
    }

    // Update the supported formats info
    if (supportedFormatsInfo) {
        const formatsList = Object.entries(SUPPORTED_FILE_TYPES).map(([mimeType, info]) => info.label);
        const uniqueFormats = [...new Set(formatsList)];
        supportedFormatsInfo.textContent = `Supported formats: ${uniqueFormats.join(', ')}`;
    }

    // Add event listener for the select file button
    if (selectFileButton) {
        selectFileButton.addEventListener('click', function() {
            fileInput.click();
        });
    }

    // Add event listener for file input change
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
    }

    // Add event listeners for drag and drop
    if (dropzone) {
        dropzone.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.add('dragover');
        });

        dropzone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');
        });

        dropzone.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');

            // Get the dropped files
            const files = e.dataTransfer.files;
            handleFiles(files);
        });
    }

    // Add event listener for the upload button
    if (uploadButton) {
        uploadButton.addEventListener('click', function() {
            uploadFiles();
        });
    }

    /**
     * Handle selected files
     *
     * @param {FileList} files - The selected files
     */
    function handleFiles(files) {
        // Clear the selected files
        if (selectedFiles) {
            selectedFiles.innerHTML = '';
        }

        // Check if any files were selected
        if (files.length === 0) {
            if (uploadButton) {
                uploadButton.disabled = true;
            }
            return;
        }

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Check if the file type is supported
            const fileTypeInfo = SUPPORTED_FILE_TYPES[file.type];
            if (!fileTypeInfo) {
                if (selectedFiles) {
                    selectedFiles.innerHTML += `
                        <div class="alert alert-danger">
                            ${file.name} is not a supported file type. Please upload one of the supported formats.
                        </div>
                    `;
                }
                continue;
            }

            // Check if the file size is within limits
            if (file.size > MAX_FILE_SIZE) {
                if (selectedFiles) {
                    selectedFiles.innerHTML += `
                        <div class="alert alert-danger">
                            ${file.name} exceeds the maximum file size of ${MAX_FILE_SIZE_DISPLAY}.
                        </div>
                    `;
                }
                continue;
            }

            // Create a file item
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi ${fileTypeInfo.icon}"></i>
                        ${file.name} (${formatFileSize(file.size)})
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;

            // Add event listener for the remove button
            const removeButton = fileItem.querySelector('.remove-file');
            removeButton.addEventListener('click', function() {
                fileItem.remove();

                // Disable the upload button if no files are selected
                if (selectedFiles && selectedFiles.children.length === 0) {
                    if (uploadButton) {
                        uploadButton.disabled = true;
                    }
                }
            });

            // Add the file item to the selected files
            if (selectedFiles) {
                selectedFiles.appendChild(fileItem);
            }
        }

        // Enable the upload button
        if (uploadButton) {
            uploadButton.disabled = false;
        }
    }

    /**
     * Upload the selected files
     */
    function uploadFiles() {
        // Get the selected file
        if (fileInput.files.length === 0) {
            alert('Please select a file to upload.');
            return;
        }

        // Get the form data
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        // Get the chunk size and overlap
        const chunkSize = document.getElementById('chunk-size')?.value || 1200;
        const overlap = document.getElementById('overlap')?.value || 0;

        // Get the extract entities and references checkboxes
        const extractEntities = document.getElementById('extract-entities')?.checked || true;
        const extractReferences = document.getElementById('extract-references')?.checked || true;

        // Add the form data
        formData.append('chunk_size', chunkSize);
        formData.append('overlap', overlap);
        formData.append('extract_entities', extractEntities);
        formData.append('extract_references', extractReferences);

        // Show the upload progress
        if (uploadProgress) {
            uploadProgress.style.display = 'block';
        }
        if (uploadResults) {
            uploadResults.style.display = 'none';
        }

        // Update the progress bar
        const progressBar = uploadProgress?.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
        }

        // Update the status
        if (uploadStatus) {
            uploadStatus.textContent = 'Uploading file...';
        }

        // Upload the file
        fetch('/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide the upload progress
            if (uploadProgress) {
                uploadProgress.style.display = 'none';
            }

            // Check if the upload was successful
            if (data.success) {
                // If background processing is enabled, start tracking progress
                if (data.entity_extraction_running || data.reference_extraction_running) {
                    // Show processing progress
                    if (processingProgress) {
                        processingProgress.style.display = 'block';
                    }

                    // Set the current document ID for progress tracking
                    currentDocumentId = data.id;

                    // Start tracking progress
                    startProgressTracking(currentDocumentId);

                    // Show initial status
                    if (processingStatus) {
                        processingStatus.textContent = 'Processing document...';
                    }
                    if (processingStep) {
                        processingStep.textContent = 'Starting document processing';
                    }

                    // Update progress bar
                    const progressBar = processingProgress?.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '5%';
                        progressBar.setAttribute('aria-valuenow', 5);
                    }
                } else {
                    // Show the upload results immediately
                    if (uploadResults) {
                        uploadResults.style.display = 'block';
                    }

                    // Display the results
                    if (uploadResultsContent) {
                        uploadResultsContent.innerHTML = `
                            <div class="alert alert-success">
                                <h5>Success!</h5>
                                <p>${data.message || 'File uploaded and processed successfully.'}</p>
                            </div>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6>File Information</h6>
                                    <p><strong>Filename:</strong> ${data.filename}</p>
                                    <p><strong>File Type:</strong> ${data.file_type || 'Document'}</p>
                                    <p><strong>Chunks Processed:</strong> ${data.chunks}</p>
                                    <p><strong>Entities Extracted:</strong> ${data.entities}</p>
                                    <p><strong>References Extracted:</strong> ${data.references}</p>
                                    <p><strong>Embeddings Generated:</strong> ${data.embeddings_generated || 0}</p>
                                    <p><strong>Embedding Model:</strong> ${data.embedding_model || 'none'}</p>
                                </div>
                            </div>
                        `;
                    }
                }

                // Clear the file input
                fileInput.value = '';
                if (selectedFiles) {
                    selectedFiles.innerHTML = '';
                }
                if (uploadButton) {
                    uploadButton.disabled = true;
                }
            } else {
                // Show the upload results for error
                if (uploadResults) {
                    uploadResults.style.display = 'block';
                }

                // Display the error
                if (uploadResultsContent) {
                    uploadResultsContent.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>Error</h5>
                            <p>${data.error || 'An error occurred during processing.'}</p>
                            <p class="mt-2">If you're seeing OCR-related errors, the system will automatically try alternative methods to process your document.</p>
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide the upload progress
            if (uploadProgress) {
                uploadProgress.style.display = 'none';
            }

            // Hide the processing progress
            if (processingProgress) {
                processingProgress.style.display = 'none';
            }

            // Stop progress tracking if it's running
            stopProgressTracking();

            // Show the upload results
            if (uploadResults) {
                uploadResults.style.display = 'block';
            }

            // Display the error
            if (uploadResultsContent) {
                uploadResultsContent.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error</h5>
                        <p>${error.message}</p>
                        <p class="mt-2">If you're seeing OCR-related errors, the system will automatically try alternative methods to process your document.</p>
                    </div>
                `;
            }
        });
    }

    /**
     * Start tracking the progress of document processing
     *
     * @param {string} documentId - The ID of the document being processed
     */
    function startProgressTracking(documentId) {
        // Clear any existing interval
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // Set up the interval to check progress
        progressInterval = setInterval(() => {
            checkDocumentProgress(documentId);
        }, 2000); // Check every 2 seconds
    }

    /**
     * Check the progress of document processing
     *
     * @param {string} documentId - The ID of the document being processed
     */
    function checkDocumentProgress(documentId) {
        fetch(`/api/document-progress/${documentId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Update the progress UI
                updateProgressUI(data);

                // If processing is complete or failed, stop tracking
                if (data.status === 'completed' || data.status === 'failed') {
                    stopProgressTracking();

                    // If completed, show the final results
                    if (data.status === 'completed') {
                        showProcessingResults(data);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking document progress:', error);
            });
    }

    /**
     * Update the progress UI with the latest progress data
     *
     * @param {Object} progressData - The progress data from the API
     */
    function updateProgressUI(progressData) {
        // Update progress bar
        const progressBar = processingProgress?.querySelector('.progress-bar');
        if (progressBar) {
            const percentage = progressData.progress_percentage || 0;
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        // Update status text
        if (processingStatus) {
            if (progressData.status === 'processing') {
                processingStatus.textContent = 'Processing document...';
            } else if (progressData.status === 'completed') {
                processingStatus.textContent = 'Processing complete!';
            } else if (progressData.status === 'failed') {
                processingStatus.textContent = 'Processing failed';
            } else if (progressData.status === 'queued') {
                processingStatus.textContent = 'Waiting in queue...';
            }
        }

        // Update step text
        if (processingStep) {
            processingStep.textContent = progressData.step_name || 'Processing...';
        }
    }

    /**
     * Stop tracking the progress of document processing
     */
    function stopProgressTracking() {
        if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
        }
    }

    /**
     * Show the final processing results
     *
     * @param {Object} progressData - The progress data from the API
     */
    function showProcessingResults(progressData) {
        // Hide the processing progress
        if (processingProgress) {
            processingProgress.style.display = 'none';
        }

        // Show the upload results
        if (uploadResults) {
            uploadResults.style.display = 'block';
        }

        // Get the details from the progress data
        const details = progressData.details || {};

        // Display the results
        if (uploadResultsContent) {
            uploadResultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h5>Success!</h5>
                    <p>File uploaded and processed successfully.</p>
                </div>
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>File Information</h6>
                        <p><strong>Filename:</strong> ${progressData.filename || 'Unknown'}</p>
                        <p><strong>File Type:</strong> document</p>
                        <p><strong>Chunks Processed:</strong> ${details.chunks || 0}</p>
                        <p><strong>Entities Extracted:</strong> ${details.entities_extracted || 0}</p>
                        <p><strong>References Extracted:</strong> ${details.references_extracted || 0}</p>
                        <p><strong>Embeddings Generated:</strong> ${details.embeddings_generated || 0}</p>
                        <p><strong>Embedding Model:</strong> ${details.embedding_model || 'none'}</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Format file size in a human-readable format
     *
     * @param {number} bytes - The file size in bytes
     * @returns {string} The formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
